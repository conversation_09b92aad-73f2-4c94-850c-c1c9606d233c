fileFormatVersion: 2
guid: e4a5be9218860274d86efaf747509362
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine02
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine01
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Animation_Walking_withSkin(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Animation_Walking_withSkin(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: 0.010942162, y: -0.050172925, z: 0.53919846}
      rotation: {x: 0.36070034, y: -0.8563437, z: 0.16493718, w: -0.33070603}
      scale: {x: 1.0000004, y: 1.0000004, z: 1.0000001}
    - name: LeftUpLeg
      parentName: Hips
      position: {x: 0.09966248, y: 0.104472175, z: -0.05732104}
      rotation: {x: -0.012827948, y: -0.64344764, z: -0.41393873, w: 0.6437899}
      scale: {x: 1, y: 1, z: 1.0000001}
    - name: LeftLeg
      parentName: LeftUpLeg
      position: {x: 0.000000014305114, y: 0.2381738, z: 0}
      rotation: {x: 0.11329319, y: -0.07680126, z: -0.046105664, w: 0.9895153}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000002}
    - name: LeftFoot
      parentName: LeftLeg
      position: {x: -0.00000003822148, y: 0.17176813, z: 0.0000000077486035}
      rotation: {x: -0.4477009, y: -0.04230173, z: -0.19580568, w: 0.87145543}
      scale: {x: 0.9999998, y: 1.0000004, z: 1.0000004}
    - name: LeftToeBase
      parentName: LeftFoot
      position: {x: -0.0000000041443853, y: 0.10011825, z: 0.0000000017788261}
      rotation: {x: -0.39023662, y: -0.13189813, z: -0.05659575, w: 0.9094587}
      scale: {x: 1.0000002, y: 1.0000007, z: 1.0000008}
    - name: RightUpLeg
      parentName: Hips
      position: {x: -0.04423081, y: -0.03600181, z: 0.13195156}
      rotation: {x: 0.04924258, y: -0.7684097, z: -0.5455875, w: 0.33084115}
      scale: {x: 1.0000002, y: 1.0000006, z: 1.0000007}
    - name: RightLeg
      parentName: RightUpLeg
      position: {x: -0.0000000011920929, y: 0.23675832, z: 0.000000009536743}
      rotation: {x: 0.11059991, y: 0.064233236, z: 0.03644348, w: 0.99111736}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000001}
    - name: RightFoot
      parentName: RightLeg
      position: {x: 0.00000002771616, y: 0.17176427, z: -0.00000002026558}
      rotation: {x: -0.44304046, y: 0.11062743, z: 0.14760101, w: 0.8773202}
      scale: {x: 1, y: 1, z: 0.9999998}
    - name: RightToeBase
      parentName: RightFoot
      position: {x: -0.00000001013279, y: 0.096190505, z: 0.0000000017881393}
      rotation: {x: -0.41568923, y: 0.05032493, z: 0.02304444, w: 0.90782094}
      scale: {x: 1.0000002, y: 0.99999994, z: 1.0000001}
    - name: Spine02
      parentName: Hips
      position: {x: -0.055431698, y: -0.0684704, z: -0.09542002}
      rotation: {x: -0.4901561, y: 0.48503122, z: -0.723437, w: 0.033626158}
      scale: {x: 0.99999964, y: 0.99999994, z: 0.9999997}
    - name: Spine01
      parentName: Spine02
      position: {x: -0.0000000016391277, y: 0.12986854, z: -0.000000011948869}
      rotation: {x: 0.00000021047887, y: -0.0000000074505797, z: -0.000000029802319, w: 1}
      scale: {x: 1.0000002, y: 1.0000001, z: 0.9999999}
    - name: Spine
      parentName: Spine01
      position: {x: -0.0000000011920929, y: 0.12986855, z: 0.0000000012665987}
      rotation: {x: -0.048909355, y: -0.0069551617, z: 0.0077234516, w: 0.99874914}
      scale: {x: 0.9999998, y: 0.99999994, z: 0.9999999}
    - name: LeftShoulder
      parentName: Spine
      position: {x: -0.042793732, y: 0.038799156, z: -0.0036202602}
      rotation: {x: -0.50751644, y: 0.5307141, z: -0.47692657, w: -0.48302245}
      scale: {x: 1.0000004, y: 1.0000007, z: 1.0000002}
    - name: LeftArm
      parentName: LeftShoulder
      position: {x: 0.000000009536743, y: 0.17490633, z: -0.000000038146972}
      rotation: {x: -0.02660645, y: 0.10576462, z: -0.0006808637, w: -0.99403495}
      scale: {x: 1.0000005, y: 1.0000005, z: 1.0000008}
    - name: LeftForeArm
      parentName: LeftArm
      position: {x: 0.00000006198883, y: 0.23770455, z: -0.000000047683713}
      rotation: {x: -0.024258344, y: 0.0012741381, z: -0.05906574, w: 0.99795854}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: LeftHand
      parentName: LeftForeArm
      position: {x: -0.00000006437301, y: 0.15321836, z: 0.000000023841856}
      rotation: {x: 0.15715413, y: -0.077387154, z: 0.3995016, w: 0.8998402}
      scale: {x: 0.99999994, y: 1.0000001, z: 1.0000002}
    - name: RightShoulder
      parentName: Spine
      position: {x: 0.042912234, y: 0.036533687, z: -0.0060858047}
      rotation: {x: 0.5070476, y: 0.531162, z: -0.50424314, w: 0.4544321}
      scale: {x: 1.0000006, y: 1.0000008, z: 1.0000004}
    - name: RightArm
      parentName: RightShoulder
      position: {x: 0.000000039339064, y: 0.16853523, z: -0.00000004053116}
      rotation: {x: 0.025012648, y: 0.11242383, z: -0.00047880394, w: 0.99334544}
      scale: {x: 1.0000004, y: 1.000001, z: 1.0000002}
    - name: RightForeArm
      parentName: RightArm
      position: {x: -0.00000009059906, y: 0.2088497, z: -0.0000000333786}
      rotation: {x: -0.050575413, y: -0.07152741, z: 0.054796234, w: 0.9946474}
      scale: {x: 0.99999994, y: 1.0000005, z: 1.0000011}
    - name: RightHand
      parentName: RightForeArm
      position: {x: 0.000000014305114, y: 0.19861381, z: -0.000000030994414}
      rotation: {x: 0.080574214, y: 0.046607997, z: -0.45784938, w: 0.8841433}
      scale: {x: 1.0000005, y: 1.0000001, z: 0.9999997}
    - name: neck
      parentName: Spine
      position: {x: -0.00011850774, y: 0.09851627, z: 0.009705981}
      rotation: {x: 0.049083762, y: -0.0019532018, z: 0.0005033192, w: 0.9987927}
      scale: {x: 1.0000005, y: 1.0000001, z: 1.0000004}
    - name: Head
      parentName: neck
      position: {x: 2.9802322e-10, y: 0.06804349, z: -0.0000000010547228}
      rotation: {x: 0.21252613, y: 0.00006705824, z: -0.0065111923, w: 0.9771337}
      scale: {x: 0.99999994, y: 0.9999998, z: 1.0000004}
    - name: head_end
      parentName: Head
      position: {x: -0.00795934, y: 0.5247182, z: -0.26411617}
      rotation: {x: -0.23103844, y: 0.000000029656796, z: 0.0069624907, w: 0.9729197}
      scale: {x: 1.0000001, y: 1.0000001, z: 1}
    - name: headfront
      parentName: Head
      position: {x: 0.007959344, y: 0.12678134, z: 0.2641164}
      rotation: {x: 0.53239965, y: 0.0000000039981383, z: -0.016044274, w: 0.8463411}
      scale: {x: 1.0000001, y: 1.0000004, z: 1}
    - name: char1
      parentName: Animation_Walking_withSkin(Clone)
      position: {x: -0.000000019073486, y: 0.00000004053116, z: 0}
      rotation: {x: 1.4432899e-15, y: 0.000000007450581, z: 0.000000004656613, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
