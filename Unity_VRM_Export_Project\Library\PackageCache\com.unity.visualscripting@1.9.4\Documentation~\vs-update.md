# Update Visual Scripting

> [!TIP]
> Back up your data before you update to a new version of Visual Scripting. For more information on how to back up your Visual Scripting assets and settings, see [Create or restore a backup](vs-create-restore-backups.md).

Before you update, confirm that the version of Visual Scripting is compatible with your current project and needs. For example, you shouldn't use a Preview version of Visual Scripting in a production environment. For more information on package states and the package lifecycle in Unity, see the [Package state and lifecycle](https://docs.unity3d.com/Manual/upm-lifecycle.html) in the Unity User Manual.

To update your current version of Visual Scripting:

1. Go to **Window** &gt; **Package Manager**. 
2. In the **Packages** drop-down menu, select **In Project**.
3. In your list of packages, select **Visual Scripting**. 
4. Select **Update to `X.X.X`**, where `X.X.X` is the newest available version of Visual Scripting. 


