namespace Unity.VisualScripting
{
    public enum AlignOperation
    {
        /// <summary>
        /// Align the left edges of the selected elements.
        /// </summary>
        Align<PERSON><PERSON>tEdges,

        /// <summary>
        /// Align the horizontal centers of the selected elements.
        /// </summary>
        AlignCenters,

        /// <summary>
        /// Align the right edges of the selected elements.
        /// </summary>
        Align<PERSON>ightEdges,

        /// <summary>
        /// Align the top edges of the selected elements.
        /// </summary>
        AlignTopEdges,

        /// <summary>
        /// Align the vertical middles of the selected elements.
        /// </summary>
        AlignMiddles,

        /// <summary>
        /// Align the bottom edges of the selected elements.
        /// </summary>
        AlignBottomEdges
    }
}
