---
title: nodes-input-system-ports
---

| **Name** | **Type** | **Description** |
| :------  | :------- | :-------------  |
| **Target**   | Player Input | The Player Input component that Visual Scripting uses to display a list of input actions. The default is **This**, which is the Player Input component attached to the GameObject where Visual Scripting runs the Script Graph. You can also connect a node that outputs a Player Input component.|
| **Input Action** | Input Action | An input action. Use the dropdown to select an input action from the Player Input component specified in **Player Input**, or connect a node that outputs an input action.|