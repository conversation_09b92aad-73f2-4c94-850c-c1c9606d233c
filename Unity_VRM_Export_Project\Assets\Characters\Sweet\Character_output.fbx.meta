fileFormatVersion: 2
guid: 778d81b2590619a4bb22eb1d0ab2073c
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine02
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine01
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Character_output(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Character_output(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: 0.009047074, y: -0.04854581, z: 0.56349915}
      rotation: {x: 0.3607003, y: -0.8563437, z: 0.16493706, w: -0.33070606}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
    - name: LeftUpLeg
      parentName: Hips
      position: {x: 0.099662505, y: 0.10447217, z: -0.057321057}
      rotation: {x: -0.012827992, y: -0.6434475, z: -0.41393882, w: 0.64378995}
      scale: {x: 1.0000002, y: 1.0000006, z: 1.0000004}
    - name: LeftLeg
      parentName: LeftUpLeg
      position: {x: 0.00000000834465, y: 0.2381738, z: -0.000000007152557}
      rotation: {x: 0.113293216, y: -0.07680133, z: -0.046105668, w: 0.9895153}
      scale: {x: 1, y: 1.0000002, z: 0.9999996}
    - name: LeftFoot
      parentName: LeftLeg
      position: {x: -0.0000000063935293, y: 0.17176816, z: 0.000000011734664}
      rotation: {x: -0.44770083, y: -0.042301252, z: -0.19580562, w: 0.8714556}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: LeftToeBase
      parentName: LeftFoot
      position: {x: 0.000000007152557, y: 0.100118265, z: 0.000000009536743}
      rotation: {x: -0.39023668, y: -0.13189828, z: -0.056595728, w: 0.90945864}
      scale: {x: 1, y: 1.0000005, z: 0.99999994}
    - name: RightUpLeg
      parentName: Hips
      position: {x: -0.044230822, y: -0.03600182, z: 0.13195156}
      rotation: {x: 0.04924251, y: -0.7684097, z: -0.54558766, w: 0.3308412}
      scale: {x: 1.0000002, y: 1.0000002, z: 0.99999994}
    - name: RightLeg
      parentName: RightUpLeg
      position: {x: 0.000000009536743, y: 0.2367582, z: -0.00000000834465}
      rotation: {x: 0.110599935, y: 0.06423325, z: 0.03644348, w: 0.99111736}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000004}
    - name: RightFoot
      parentName: RightLeg
      position: {x: 0.000000012260862, y: 0.1717643, z: -0.000000015683472}
      rotation: {x: -0.44304046, y: 0.11062737, z: 0.14760095, w: 0.8773203}
      scale: {x: 1, y: 1, z: 1.0000002}
    - name: RightToeBase
      parentName: RightFoot
      position: {x: -0.000000019371509, y: 0.09619053, z: 0.0000000035762786}
      rotation: {x: -0.41568932, y: 0.05032498, z: 0.023044504, w: 0.9078209}
      scale: {x: 0.99999994, y: 1.0000006, z: 1.0000004}
    - name: Spine02
      parentName: Hips
      position: {x: -0.05543171, y: -0.06847044, z: -0.09542002}
      rotation: {x: -0.4901561, y: 0.48503125, z: -0.72343695, w: 0.033626143}
      scale: {x: 0.9999999, y: 0.9999999, z: 0.9999997}
    - name: Spine01
      parentName: Spine02
      position: {x: -2.2351741e-10, y: 0.12986866, z: -0.000000002249144}
      rotation: {x: 0.00000010989605, y: 0.0000000074505797, z: -0.000000014901159, w: 1}
      scale: {x: 1, y: 1.0000001, z: 0.9999999}
    - name: Spine
      parentName: Spine01
      position: {x: -2.9802322e-10, y: 0.1298687, z: 0.0000000016065314}
      rotation: {x: -0.048909336, y: -0.006955129, z: 0.007723466, w: 0.9987492}
      scale: {x: 1.0000001, y: 1, z: 0.9999999}
    - name: LeftShoulder
      parentName: Spine
      position: {x: -0.04279372, y: 0.038798988, z: -0.0036202602}
      rotation: {x: -0.50751644, y: 0.53071404, z: -0.47692662, w: -0.48302245}
      scale: {x: 1.0000005, y: 1.0000006, z: 1.0000006}
    - name: LeftArm
      parentName: LeftShoulder
      position: {x: 0.0000000020577766, y: 0.17490636, z: 0.0000000050893676}
      rotation: {x: -0.026606923, y: 0.105764784, z: -0.00068104244, w: -0.9940349}
      scale: {x: 1, y: 1.0000001, z: 0.9999998}
    - name: LeftForeArm
      parentName: LeftArm
      position: {x: 0.000000012516975, y: 0.23770456, z: -0.000000074878336}
      rotation: {x: -0.024259035, y: 0.0012741531, z: -0.05906513, w: 0.99795854}
      scale: {x: 0.99999994, y: 1, z: 0.9999998}
    - name: LeftHand
      parentName: LeftForeArm
      position: {x: 0.000000024139881, y: 0.1532184, z: 0.00000011768192}
      rotation: {x: 0.1571541, y: -0.07738729, z: 0.3995016, w: 0.8998402}
      scale: {x: 0.9999998, y: 0.9999997, z: 1.0000002}
    - name: RightShoulder
      parentName: Spine
      position: {x: 0.04291222, y: 0.036533616, z: -0.006085812}
      rotation: {x: 0.5070477, y: 0.531162, z: -0.504243, w: 0.45443207}
      scale: {x: 1.0000007, y: 1.0000005, z: 1.0000008}
    - name: RightArm
      parentName: RightShoulder
      position: {x: 0.000000006336426, y: 0.1685352, z: -0.00000007999305}
      rotation: {x: 0.025012588, y: 0.112423874, z: -0.0004782377, w: 0.9933454}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: RightForeArm
      parentName: RightArm
      position: {x: 0.00000000923872, y: 0.20884961, z: 0.000000011473894}
      rotation: {x: -0.05057495, y: -0.07152759, z: 0.054795694, w: 0.99464744}
      scale: {x: 1.0000001, y: 1, z: 1.0000004}
    - name: RightHand
      parentName: RightForeArm
      position: {x: 0.0000000062584875, y: 0.19861391, z: -0.00000006960705}
      rotation: {x: 0.0805742, y: 0.046608064, z: -0.45784932, w: 0.8841434}
      scale: {x: 1.0000001, y: 0.9999998, z: 1}
    - name: neck
      parentName: Spine
      position: {x: -0.00011850618, y: 0.09851616, z: 0.0097060045}
      rotation: {x: 0.049083903, y: -0.0019532286, z: 0.0005032884, w: 0.99879265}
      scale: {x: 0.9999999, y: 1.0000002, z: 1.0000001}
    - name: Head
      parentName: neck
      position: {x: 4.4703483e-10, y: 0.068043515, z: 0.0000000050896776}
      rotation: {x: 0.21252611, y: 0.000067057896, z: -0.006511201, w: 0.9771337}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: head_end
      parentName: Head
      position: {x: -0.00795934, y: 0.5247182, z: -0.2641163}
      rotation: {x: -0.23103856, y: -0.000000009684299, z: 0.0069625396, w: 0.97291976}
      scale: {x: 1, y: 0.9999998, z: 1.0000001}
    - name: headfront
      parentName: Head
      position: {x: 0.007959344, y: 0.12678123, z: 0.2641163}
      rotation: {x: 0.53239965, y: 0.000000008458301, z: -0.016044274, w: 0.84634113}
      scale: {x: 1, y: 1.0000001, z: 0.9999997}
    - name: char1
      parentName: Character_output(Clone)
      position: {x: -0.000000019073486, y: 0.00000004053116, z: 0}
      rotation: {x: 1.4432899e-15, y: 0.000000007450581, z: 0.000000004656613, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
