---
title: nodes-input-action-change
---

<table>
<thead>
<tr>
<th><strong>Name</strong></th>
<th><strong>Type</strong></th>
<th colspan="2"><strong>Description</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td rowspan="4"><strong>Input Action Change Type</strong></td>
<td rowspan="4">Input Action Change Option</td>
<td colspan="2">Set an Input Action Change Type to choose the interaction type that triggers the node.</td>
</tr>
<tr>
<td><strong>On Pressed</strong></td>
<td>The node triggers when a user presses the button from the selected <strong>Input Action</strong> input asset.</td>
</tr>
<tr>
<td><strong>On Hold</strong></td>
<td>The node triggers when a user holds the button from the selected <strong>Input Action</strong> input asset.</td>
</tr>
<tr>
<td><strong>On Released</strong></td>
<td>The node triggers when a user releases the button from the selected <strong>Input Action</strong> input asset.</td>
</tr>
</tbody>
</table>