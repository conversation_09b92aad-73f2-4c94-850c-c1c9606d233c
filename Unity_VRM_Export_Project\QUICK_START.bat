@echo off
echo ========================================
echo Unity VRM Export Project - Quick Start
echo ========================================
echo.
echo 🚀 READY TO GO! Your Unity VRM Export Project is set up.
echo.
echo 📁 Project Structure:
echo   ✓ Unity project files created
echo   ✓ Character assets copied
echo   ✓ Editor scripts installed
echo   ✓ Export scene ready
echo.
echo 🎯 Next Steps:
echo.
echo 1. Open Unity Hub
echo 2. Click "Add" and select this folder:
echo    %cd%
echo.
echo 3. Open the project in Unity 2021.3 LTS
echo.
echo 4. Import UniVRM package:
echo    - Download from: https://github.com/vrm-c/UniVRM/releases
echo    - Assets → Import Package → Custom Package...
echo.
echo 5. Start exporting VRMs:
echo    - VRM Tools → Create Empty VRM Scene
echo    - Drag character into scene
echo    - VRM Tools → One-Click Export Selected Character
echo.
echo 📋 Your Characters Ready for Export:
echo   🎭 Grimm (grimm.glb)
echo   💀 Abyss (hooded figure with scythe)  
echo   👤 Sweet (Character_output.fbx)
echo   🚶 Walk Animation (Animation_Walking_withSkin.fbx)
echo.
echo 📖 For detailed instructions, see:
echo   - README.md
echo   - SETUP_GUIDE.md
echo   - PROJECT_SUMMARY.md
echo.
echo Press any key to open Unity Hub...
pause >nul

echo.
echo Opening Unity Hub...
start "" "unityhub://projects"

echo.
echo If Unity Hub doesn't open automatically:
echo 1. Open Unity Hub manually
echo 2. Click "Add" 
echo 3. Select this folder: %cd%
echo 4. Open the project
echo.
echo Happy VRM exporting! 🎉
pause
