# Advanced customization and development

> [!NOTE] 
> For versions 2019/2020 LTS, download the Visual Scripting package from the [Unity Asset Store](https://assetstore.unity.com/packages/tools/visual-bolt-163802).

You can customize and extend the basic functionality of Visual Scripting using C# code. 

## Create a Script Graph node 

With a C# script, you can [create your own Custom C# node](vs-create-custom-node.md) and add more functionality to your Visual Scripting graphs. 

## Create a custom event node 

You can trigger logic in your application with [a custom event node](vs-create-own-custom-event-node.md). 

## Add custom types to Visual Scripting 

Add your own [custom classes and types in Visual Scripting](vs-custom-types.md) to store information more efficiently. 