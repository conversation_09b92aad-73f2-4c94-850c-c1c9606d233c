# Develop logic transitions with State Graphs

You can use State Graphs to change behaviors of GameObjects based on specific conditions. 

## Create a new state 

After you [create a new graph file](vs-create-graph.md) for a State Graph, you can [create states](vs-create-state.md) to tell Visual Scripting what a GameObject does, and when. 

## Create a transition 

Use [transitions](vs-transitions.md) to tell Visual Scripting when a GameObject changes states. There's no restriction on how many transitions you can create. 

## State Unit nodes 

You can use a [State Unit node](vs-nesting-state-unit-node.md) to nest a State Graph inside a Script Graph. 