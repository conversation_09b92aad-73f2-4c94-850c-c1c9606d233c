---
uid: assistant-feedback
---

# Provide feedback in Assistant

You can improve the quality and relevance of future responses by identifying which answers were helpful and which weren't.

To provide feedback, follow these steps:

1. Find the response for which you want to provide feedback.
1. Choose one of the following:

   * **Good response**: Select to mark the answer as helpful.
   * **Poor response**: Select to mark the answer as unhelpful.

When you select **Good response** or **Poor response**, your feedback is immediately sent and saved. You will know it's recorded when the selected button stays highlighted in blue.

**(Optional) Add more details**
After you select **Good response** or **Poor response**, an **Add a comment** section appears. You can use it to provide more context or suggestions as follows:

1. Select **Add a comment** to open the text field.
1. Enter your comment.
1. Select **Send**.

If you select **Poor response** and the answer is offensive, inappropriate, or harmful, you can also select **Flag as inappropriate**.

After you submit your feedback, the **Feedback sent** message appears. This indicates that the Assistant support team has received your feedback.

## Additional resources

* [Get started with Assistant](xref:get-started)
* [Use /ask mode](xref:ask-overview)