@echo off
echo ========================================
echo Unity VRM Export Project - Asset Copier
echo ========================================
echo.
echo This script will copy your character assets from the main project
echo to the Unity VRM Export Project structure.
echo.

REM Get the current directory (should be Unity_VRM_Export_Project)
set "UNITY_PROJECT=%cd%"
echo Unity Project Location: %UNITY_PROJECT%
echo.

REM Go up one level to find the main project
cd ..
set "MAIN_PROJECT=%cd%"
echo Main Project Location: %MAIN_PROJECT%
echo.

REM Check if source files exist
echo Checking for source files...

if exist "%MAIN_PROJECT%\PGD\grimm.glb" (
    echo ✓ Found: PGD\grimm.glb
) else (
    echo ✗ Missing: PGD\grimm.glb
)

if exist "%MAIN_PROJECT%\mrabyss\hooded-figure-with-scythe" (
    echo ✓ Found: mrabyss\hooded-figure-with-scythe
) else (
    echo ✗ Missing: mrabyss\hooded-figure-with-scythe
)

if exist "%MAIN_PROJECT%\biped\Character_output.fbx" (
    echo ✓ Found: biped\Character_output.fbx
) else (
    echo ✗ Missing: biped\Character_output.fbx
)

if exist "%MAIN_PROJECT%\biped\Animation_Walking_withSkin.fbx" (
    echo ✓ Found: biped\Animation_Walking_withSkin.fbx
) else (
    echo ✗ Missing: biped\Animation_Walking_withSkin.fbx
)

echo.
echo Press any key to continue with copying, or Ctrl+C to cancel...
pause >nul

echo.
echo Starting copy operations...

REM Copy Grimm assets
echo.
echo Copying Grimm assets...
if exist "%MAIN_PROJECT%\PGD\grimm.glb" (
    copy "%MAIN_PROJECT%\PGD\grimm.glb" "%UNITY_PROJECT%\Assets\Characters\Grimm\"
    echo ✓ Copied grimm.glb
)
if exist "%MAIN_PROJECT%\PGD\Grimm.png" (
    copy "%MAIN_PROJECT%\PGD\Grimm.png" "%UNITY_PROJECT%\Assets\Characters\Grimm\"
    echo ✓ Copied Grimm.png
)

REM Copy Abyss assets
echo.
echo Copying Abyss assets...
if exist "%MAIN_PROJECT%\mrabyss\hooded-figure-with-scythe" (
    xcopy "%MAIN_PROJECT%\mrabyss\hooded-figure-with-scythe" "%UNITY_PROJECT%\Assets\Characters\Abyss\" /E /I /Y
    echo ✓ Copied hooded-figure-with-scythe folder
)

REM Copy additional Abyss images
if exist "%MAIN_PROJECT%\mrabyss\abyss.png" (
    copy "%MAIN_PROJECT%\mrabyss\abyss.png" "%UNITY_PROJECT%\Assets\Characters\Abyss\"
    echo ✓ Copied abyss.png
)
if exist "%MAIN_PROJECT%\mrabyss\abyss2.png" (
    copy "%MAIN_PROJECT%\mrabyss\abyss2.png" "%UNITY_PROJECT%\Assets\Characters\Abyss\"
    echo ✓ Copied abyss2.png
)

REM Copy Sweet character
echo.
echo Copying Sweet character...
if exist "%MAIN_PROJECT%\biped\Character_output.fbx" (
    copy "%MAIN_PROJECT%\biped\Character_output.fbx" "%UNITY_PROJECT%\Assets\Characters\Sweet\"
    echo ✓ Copied Character_output.fbx
)

REM Copy animations
echo.
echo Copying animations...
if exist "%MAIN_PROJECT%\biped\Animation_Walking_withSkin.fbx" (
    copy "%MAIN_PROJECT%\biped\Animation_Walking_withSkin.fbx" "%UNITY_PROJECT%\Assets\Animations\"
    echo ✓ Copied Animation_Walking_withSkin.fbx
)

echo.
echo ========================================
echo Copy operations completed!
echo ========================================
echo.
echo Next steps:
echo 1. Open Unity and load this project
echo 2. Import UniVRM package
echo 3. Use VRM Tools menu for export workflow
echo.
echo Press any key to exit...
pause >nul
