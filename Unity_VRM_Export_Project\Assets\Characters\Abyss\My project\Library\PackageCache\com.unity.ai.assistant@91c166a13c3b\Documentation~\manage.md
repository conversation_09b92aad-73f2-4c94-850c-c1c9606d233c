---
uid: manage-assistant
---

# Manage Assistant

Assistant provides several features to help you manage interactions, organize responses, and refine AI-generated outputs. These features apply across all Assistant modes ([**/ask**](xref:ask-overview), [**/code**](xref:code-overview), and [**/run**](xref:run-overview)).

Use the following features to manage Assistant:

| Feature | Description |
| ------- | ----------- |
| [Use Assistant history](xref:assistant-history) | View and revisit past interactions to track responses and reuse queries. |
| [Manage messages and code snippets](xref:assistant-code) | Copy, save, and organize responses, including code snippets and task summaries. |
| [Use sources in Assistant](xref:assistant-sources) | Refer to citations for AI-generated responses and understand where the information comes from. |
| [Provide feedback in Assistant](xref:assistant-feedback) | Rate responses, report issues, and improve AI-generated results over time. |
| [Interact with GameObjects, assets, and console errors](xref:assistant-object-query) | Attach GameObjects, assets, or error logs to queries for context-aware help. |

## Additional resources

* [Assistant interface](xref:assistant-interface)
* [Best practices for using Assistant](xref:assistant-best)