---
uid: run-overview
---

# Use /run mode

The **/run** mode in Assistant automates actions in the Unity Editor to help you with repetitive tasks. It generates and runs C# scripts within the Unity Editor. It also validates the code for errors and runs tasks that integrate with Unity.

The **/run** mode handles the following tasks:

* Runs the code generated by <PERSON>.
* Validates the generated code before it's run to ensure successful compilation.
* Debugs workflows by identifying issues in the generated code.
* Performs repetitive or complex tasks, such as modify GameObjects, duplicate assets, or adjust component properties without manual intervention.
* Edits, recompiles, and re-runs the code iteratively for refinement.

## How /run mode works

When you enter a command in **/run** mode, Assistant:

* Processes the input prompt to determine the intended action.
* Generates and displays a summary before it runs the command.
* Runs the command within the Unity Editor when instructed.
* Provides a log of changes made to the project.

## Query with /run mode

To interact with Assistant in **/run** mode, follow these steps:

1. To begin a new conversation, select **+ Chat**.
1. Use one of the following methods to switch to the **/run** mode:

   * **From [**/ask**](xref:ask-overview) mode**: If Assistant detects a command that can be run, it displays a **Run command** button next to the response. If you select this button, the command runs immediately but Assistant doesn't switch to **/run** mode for future inputs.
   * **Use a command**: Type `/run` in the text field and press **Enter**. This prepares Assistant for future commands in **/run** mode and automatically adds the **/run** prefix to subsequent prompts.
   * **Use **Shortcuts****: Select **Shortcuts** > **/run** from the Assistant menu. This also populates the input field with the **/run** prefix and keeps Assistant in **/run** mode for future commands.

1. Enter your request in the text field. For example, `Duplicate this cube 10 times and position them randomly`.
1. Press **Enter** on your keyboard or select the send icon.

   You can also [Enable and use the Ctrl+Enter (macOS: ⌘Return) Preferences option](xref:preferences) to send your prompt to Assistant.

   Assistant displays the following information:
   * Summary of the task to be performed. You can modify the parameters before the command is run.
   * Command logic or the code script.
1. Select the **Run** button to run the action in Unity.

   After running the command, Assistant provides a log with the following details:
   * Lists the GameObjects that are modified or created.
   * Describes the changes made, for example, `10 cubes duplicated`.
   * Displays messages if the operation was unsuccessful.

## Undo actions in /run mode

The **Undo History** button appears next to the results. It lets you view and revert recent actions performed using the **/run** mode.

## Additional resources

* [Assistant interface](xref:assistant-interface)
* [Best practices for using Assistant](xref:assistant-best)
* [Enable and use the Ctrl+Enter (macOS: ⌘Return) Preferences option](xref:preferences)