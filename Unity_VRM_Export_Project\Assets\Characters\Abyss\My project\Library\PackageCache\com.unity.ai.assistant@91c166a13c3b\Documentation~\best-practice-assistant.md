---
uid: assistant-best
---

# Best practices for using Assistant

Use these best practices to ensure efficient and productive use of Assistant.

### Provide clear and specific queries

**Be precise**: Clearly state what you need help with. Provide specific details about the problem or task. Begin a new conversation for different topics for optimal results.

For example, use `How do I fix the 'NullReferenceException' in my player movement script?` instead of `How do I fix this script?`.

**Use context**: Include relevant context in your questions to help Assistant understand your query better.

For example, `How do I change the material of a GameObject at runtime in Unity?`.

**More context-specific queries**: You can specifically refer to GameObjects in the Scene **Hierarchy** when you ask <PERSON> a question.

For example, `Can you tell me why the Sphere object does not interact with the Plane object in my scene?`.

### Leverage code generation and debugging

**Use snippets**: Take advantage of Assistant’s ability to generate code snippets. Provide clear requirements and review the code for any necessary adjustments.

For example, `Can you provide a C# script to rotate a GameObject continuously?`.

**Debugging help**: If you encounter an error, describe the issue and the relevant code section to get accurate debugging help.

For example, `Why do I get a 'NullReferenceException' in this script when I try to access the player object?`.

**More debugging help**: You can select one or more issues from the **Console** window and ask Assistant to provide help in solving those issues.

For example, `Can you help me solve these Console issues?`.

### Integrate with Unity’s documentation

**Quick access**: Use Assistant to access Unity’s documentation and tutorials. Ask for links or summaries of relevant documentation.

For example, `Can you link me to the Unity documentation on Rigidbody components?`.

**Learning resources**: Request tutorials or best practices for specific features to enhance your skills.

For example, `Can you recommend a tutorial on creating custom shaders in Unity?`.

### Optimize workflow with recommendations

**Workflow tips**: Ask Assistant for tips on optimizing your workflow within Unity. This can include shortcuts, best practices, and efficient use of tools.

For example, `What are some best practices for organizing assets in a large Unity project?`.

**Automate tasks**: Inquire about automating repetitive tasks to save time and reduce errors.

For example, `How can I automate the process of importing and configuring textures?`.

### Customize Assistant settings

**Tailor responses**: Adjust Assistant’s settings to better suit your specific needs and preferences. This customization enhances the relevance of the help provided.

For example, `How can I customize Assistant to prioritize certain types of responses?`.

**Feedback**: Provide feedback on Assistant’s responses to help improve its performance. This helps to refine the artificial intelligence's (AI) understanding of your queries.

For example, use feedback options to report inaccurate or unhelpful responses.

### Stay updated

**Regular updates**: Keep both Unity and Assistant updated to the latest versions. Updates often include new features, improvements, and bug fixes.

**Release notes**: Review release notes for new features and enhancements to stay informed about the latest capabilities of Assistant.

Follow these best practices to enhance your productivity and efficiency when using Assistant in Unity.

## Additional resources

* [Getting started with Assistant](xref:get-started)
* [Assistant interface](xref:assistant-interface)