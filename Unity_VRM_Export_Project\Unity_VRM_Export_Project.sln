﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniGLTF", "UniGLTF.csproj", "{630F1B9B-E9A3-E66B-6C07-5C94315A6FF1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VRM10", "VRM10.csproj", "{283FD6B4-3C00-7380-CBBD-4F84210917A4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VRM10.MToon10", "VRM10.MToon10.csproj", "{6FFE3218-C51D-2C41-1B69-8A9255408885}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniHumanoid", "UniHumanoid.csproj", "{434562A1-2204-1600-686F-028888AE6B1D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VrmLib", "VrmLib.csproj", "{97E1AA89-67F4-0D7C-D8DF-92BF0D391394}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SpringBoneJobs", "SpringBoneJobs.csproj", "{3380FD96-644D-610D-8B62-3C4B7FA75F56}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniGLTF.UniUnlit", "UniGLTF.UniUnlit.csproj", "{386D4125-FE2B-5B2F-79C0-000FD7031B30}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniGLTF.Utils", "UniGLTF.Utils.csproj", "{D10C3FA4-714B-C2DC-3737-149A78529686}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{DC2D94ED-AAC0-6C0E-98FF-606EEF693C9B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FastSpringBone10", "FastSpringBone10.csproj", "{EB580C82-04F5-C6F0-CE9C-00E81F7E8570}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniGLTF.Tests", "UniGLTF.Tests.csproj", "{DCEFED4C-63CC-A7EF-1017-E173D3E751E3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniGLTF.UniUnlit.Editor", "UniGLTF.UniUnlit.Editor.csproj", "{6E5B0745-44EE-C470-E6AC-51B2B4931CA9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VRM10.Tests", "VRM10.Tests.csproj", "{CA32A181-3A3B-4F77-86E3-9F6C75E002B0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniGLTF.Editor", "UniGLTF.Editor.csproj", "{3D871C80-0DEB-23B8-D75C-99449B82AA4F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VRM10.Editor", "VRM10.Editor.csproj", "{BE5D9FC7-3938-14B0-2CA1-C4ABC20E0A23}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniHumanoid.Editor", "UniHumanoid.Editor.csproj", "{4BAB2ED8-C089-DDF1-1A74-BA5B6AA40387}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VrmLibTests", "VrmLibTests.csproj", "{4A37D8D4-8E12-8CA7-6805-9BE20E8FAED4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-Editor", "Assembly-CSharp-Editor.csproj", "{BC6B2E9F-A810-C30B-56C1-5B6E90A0B350}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VRM10.MToon10.Editor", "VRM10.MToon10.Editor.csproj", "{7B58B88D-4220-8C2C-1245-8988A3D616E5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VRM10.MToon10.Tests", "VRM10.MToon10.Tests.csproj", "{D19CC3CB-E652-DE45-02DB-151F4461D74A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniHumanoid.Editor.Tests", "UniHumanoid.Editor.Tests.csproj", "{74F38E5A-53D4-4834-7028-F2CF2C1C86E5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{630F1B9B-E9A3-E66B-6C07-5C94315A6FF1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{630F1B9B-E9A3-E66B-6C07-5C94315A6FF1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{630F1B9B-E9A3-E66B-6C07-5C94315A6FF1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{630F1B9B-E9A3-E66B-6C07-5C94315A6FF1}.Release|Any CPU.Build.0 = Release|Any CPU
		{283FD6B4-3C00-7380-CBBD-4F84210917A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{283FD6B4-3C00-7380-CBBD-4F84210917A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{283FD6B4-3C00-7380-CBBD-4F84210917A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{283FD6B4-3C00-7380-CBBD-4F84210917A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FFE3218-C51D-2C41-1B69-8A9255408885}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FFE3218-C51D-2C41-1B69-8A9255408885}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FFE3218-C51D-2C41-1B69-8A9255408885}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FFE3218-C51D-2C41-1B69-8A9255408885}.Release|Any CPU.Build.0 = Release|Any CPU
		{434562A1-2204-1600-686F-028888AE6B1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{434562A1-2204-1600-686F-028888AE6B1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{434562A1-2204-1600-686F-028888AE6B1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{434562A1-2204-1600-686F-028888AE6B1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{97E1AA89-67F4-0D7C-D8DF-92BF0D391394}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97E1AA89-67F4-0D7C-D8DF-92BF0D391394}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97E1AA89-67F4-0D7C-D8DF-92BF0D391394}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97E1AA89-67F4-0D7C-D8DF-92BF0D391394}.Release|Any CPU.Build.0 = Release|Any CPU
		{3380FD96-644D-610D-8B62-3C4B7FA75F56}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3380FD96-644D-610D-8B62-3C4B7FA75F56}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3380FD96-644D-610D-8B62-3C4B7FA75F56}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3380FD96-644D-610D-8B62-3C4B7FA75F56}.Release|Any CPU.Build.0 = Release|Any CPU
		{386D4125-FE2B-5B2F-79C0-000FD7031B30}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{386D4125-FE2B-5B2F-79C0-000FD7031B30}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{386D4125-FE2B-5B2F-79C0-000FD7031B30}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{386D4125-FE2B-5B2F-79C0-000FD7031B30}.Release|Any CPU.Build.0 = Release|Any CPU
		{D10C3FA4-714B-C2DC-3737-149A78529686}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D10C3FA4-714B-C2DC-3737-149A78529686}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D10C3FA4-714B-C2DC-3737-149A78529686}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D10C3FA4-714B-C2DC-3737-149A78529686}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC2D94ED-AAC0-6C0E-98FF-606EEF693C9B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC2D94ED-AAC0-6C0E-98FF-606EEF693C9B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC2D94ED-AAC0-6C0E-98FF-606EEF693C9B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC2D94ED-AAC0-6C0E-98FF-606EEF693C9B}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB580C82-04F5-C6F0-CE9C-00E81F7E8570}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB580C82-04F5-C6F0-CE9C-00E81F7E8570}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB580C82-04F5-C6F0-CE9C-00E81F7E8570}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB580C82-04F5-C6F0-CE9C-00E81F7E8570}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCEFED4C-63CC-A7EF-1017-E173D3E751E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCEFED4C-63CC-A7EF-1017-E173D3E751E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCEFED4C-63CC-A7EF-1017-E173D3E751E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCEFED4C-63CC-A7EF-1017-E173D3E751E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E5B0745-44EE-C470-E6AC-51B2B4931CA9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E5B0745-44EE-C470-E6AC-51B2B4931CA9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E5B0745-44EE-C470-E6AC-51B2B4931CA9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E5B0745-44EE-C470-E6AC-51B2B4931CA9}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA32A181-3A3B-4F77-86E3-9F6C75E002B0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA32A181-3A3B-4F77-86E3-9F6C75E002B0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA32A181-3A3B-4F77-86E3-9F6C75E002B0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA32A181-3A3B-4F77-86E3-9F6C75E002B0}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D871C80-0DEB-23B8-D75C-99449B82AA4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D871C80-0DEB-23B8-D75C-99449B82AA4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D871C80-0DEB-23B8-D75C-99449B82AA4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D871C80-0DEB-23B8-D75C-99449B82AA4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE5D9FC7-3938-14B0-2CA1-C4ABC20E0A23}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE5D9FC7-3938-14B0-2CA1-C4ABC20E0A23}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE5D9FC7-3938-14B0-2CA1-C4ABC20E0A23}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE5D9FC7-3938-14B0-2CA1-C4ABC20E0A23}.Release|Any CPU.Build.0 = Release|Any CPU
		{4BAB2ED8-C089-DDF1-1A74-BA5B6AA40387}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4BAB2ED8-C089-DDF1-1A74-BA5B6AA40387}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4BAB2ED8-C089-DDF1-1A74-BA5B6AA40387}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4BAB2ED8-C089-DDF1-1A74-BA5B6AA40387}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A37D8D4-8E12-8CA7-6805-9BE20E8FAED4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A37D8D4-8E12-8CA7-6805-9BE20E8FAED4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A37D8D4-8E12-8CA7-6805-9BE20E8FAED4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A37D8D4-8E12-8CA7-6805-9BE20E8FAED4}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC6B2E9F-A810-C30B-56C1-5B6E90A0B350}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC6B2E9F-A810-C30B-56C1-5B6E90A0B350}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC6B2E9F-A810-C30B-56C1-5B6E90A0B350}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC6B2E9F-A810-C30B-56C1-5B6E90A0B350}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B58B88D-4220-8C2C-1245-8988A3D616E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B58B88D-4220-8C2C-1245-8988A3D616E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B58B88D-4220-8C2C-1245-8988A3D616E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B58B88D-4220-8C2C-1245-8988A3D616E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{D19CC3CB-E652-DE45-02DB-151F4461D74A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D19CC3CB-E652-DE45-02DB-151F4461D74A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D19CC3CB-E652-DE45-02DB-151F4461D74A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D19CC3CB-E652-DE45-02DB-151F4461D74A}.Release|Any CPU.Build.0 = Release|Any CPU
		{74F38E5A-53D4-4834-7028-F2CF2C1C86E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74F38E5A-53D4-4834-7028-F2CF2C1C86E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74F38E5A-53D4-4834-7028-F2CF2C1C86E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74F38E5A-53D4-4834-7028-F2CF2C1C86E5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
