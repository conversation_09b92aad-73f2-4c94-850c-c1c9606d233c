using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Unity.AI.Assistant.UI.Editor")]
[assembly: InternalsVisibleTo("Unity.AI.Assistant.DeveloperTools")]
[assembly: InternalsVisibleTo("Unity.AI.Assistant.DeveloperTools.Tests")]
[assembly: InternalsVisibleTo("Unity.AI.Assistant.Tests")]
[assembly: InternalsVisibleTo("Unity.AI.Assistant.Tests.E2E")]
[assembly: InternalsVisibleTo("Unity.AI.Assistant.Benchmark.Tests")]
[assembly: InternalsVisibleTo("Unity.AI.Assistant.CodeLibrary.Editor")]

// Required for advanced mocking with Moq
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")]
