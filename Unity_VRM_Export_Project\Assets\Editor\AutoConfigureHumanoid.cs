using UnityEditor;
using UnityEngine;

/// <summary>
/// Automatically configures imported models as Humanoid rigs when placed in Characters/ or Animations/ folders.
/// This saves time by ensuring all character models are properly rigged for VRM export.
/// </summary>
public class AutoConfigureHumanoid : AssetPostprocessor
{
    void OnPreprocessModel()
    {
        var modelImporter = assetImporter as ModelImporter;
        if (modelImporter == null) return;

        // Only auto-configure files in specific folders
        if (!assetPath.Contains("Assets/Characters") && !assetPath.Contains("Assets/Animations")) 
            return;

        Debug.Log($"[AutoConfigureHumanoid] Configuring {assetPath} as Humanoid rig");

        // Force Humanoid rig for characters/animations
        modelImporter.animationType = ModelImporterAnimationType.Human;
        modelImporter.avatarSetup = ModelImporterAvatarSetup.CreateFromThisModel;
        
        // Keep animation clips for FBX files
        modelImporter.importAnimation = true;
        
        // Optimize for VRM export
        modelImporter.importBlendShapes = true;  // Keep facial expressions
        modelImporter.importNormals = ModelImporterNormals.Import;
        modelImporter.importTangents = ModelImporterTangents.CalculateMikk;
        
        // Material settings for better VRM compatibility
        modelImporter.materialImportMode = ModelImporterMaterialImportMode.ImportStandard;
        modelImporter.materialLocation = ModelImporterMaterialLocation.InPrefab;
        
        // Scale settings - adjust if your models are too large/small
        // modelImporter.globalScale = 1.0f;  // Uncomment and adjust if needed
        
        Debug.Log($"[AutoConfigureHumanoid] Configured {assetPath} - Animation Type: {modelImporter.animationType}");
    }
    
    void OnPostprocessModel(GameObject go)
    {
        // Only process files in our target folders
        if (!assetPath.Contains("Assets/Characters") && !assetPath.Contains("Assets/Animations")) 
            return;
            
        // Add helpful components for VRM export
        var animator = go.GetComponent<Animator>();
        if (animator == null)
        {
            animator = go.AddComponent<Animator>();
            Debug.Log($"[AutoConfigureHumanoid] Added Animator component to {go.name}");
        }
        
        // Ensure the avatar is properly assigned
        if (animator.avatar == null)
        {
            var modelImporter = AssetImporter.GetAtPath(assetPath) as ModelImporter;
            if (modelImporter != null && modelImporter.animationType == ModelImporterAnimationType.Human)
            {
                Debug.LogWarning($"[AutoConfigureHumanoid] {go.name} has no avatar assigned. Check rig configuration.");
            }
        }
        
        Debug.Log($"[AutoConfigureHumanoid] Post-processed {go.name} - Ready for VRM export");
    }
}
