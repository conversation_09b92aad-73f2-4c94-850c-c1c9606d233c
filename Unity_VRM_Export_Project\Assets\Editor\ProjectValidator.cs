using UnityEditor;
using UnityEngine;
using System.IO;

/// <summary>
/// Validates the VRM Export Project setup and provides helpful feedback.
/// Ensures all components are properly configured for VRM export workflow.
/// </summary>
public static class ProjectValidator
{
    [MenuItem("VRM Tools/Validate Project Setup")]
    public static void ValidateProjectSetup()
    {
        var report = new System.Text.StringBuilder();
        var issues = new System.Collections.Generic.List<string>();
        var warnings = new System.Collections.Generic.List<string>();
        
        report.AppendLine("🔍 VRM Export Project Validation Report");
        report.AppendLine("=====================================");
        report.AppendLine();
        
        // Check Unity version
        var unityVersion = Application.unityVersion;
        if (unityVersion.StartsWith("2021.3"))
        {
            report.AppendLine("✅ Unity Version: " + unityVersion + " (Recommended for UniVRM)");
        }
        else
        {
            warnings.Add("Unity version " + unityVersion + " - UniVRM recommends 2021.3 LTS");
            report.AppendLine("⚠️  Unity Version: " + unityVersion + " (UniVRM recommends 2021.3 LTS)");
        }
        
        // Check for UniVRM
        bool hasUniVRM = false;
        var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
        foreach (var assembly in assemblies)
        {
            if (assembly.FullName.Contains("VRM"))
            {
                hasUniVRM = true;
                break;
            }
        }
        
        if (hasUniVRM)
        {
            report.AppendLine("✅ UniVRM Package: Detected and loaded");
        }
        else
        {
            issues.Add("UniVRM package not found - download from https://github.com/vrm-c/UniVRM/releases");
            report.AppendLine("❌ UniVRM Package: Not found");
            report.AppendLine("   📥 Download from: https://github.com/vrm-c/UniVRM/releases");
        }
        
        report.AppendLine();
        report.AppendLine("📁 Character Assets:");
        
        // Check character assets
        var grimmPath = "Assets/Characters/Grimm/grimm.glb";
        if (File.Exists(Path.Combine(Application.dataPath, "../", grimmPath)))
        {
            report.AppendLine("✅ Grimm: grimm.glb found");
        }
        else
        {
            issues.Add("Grimm character file missing: " + grimmPath);
            report.AppendLine("❌ Grimm: grimm.glb not found");
        }
        
        var abyssPath = "Assets/Characters/Abyss";
        if (Directory.Exists(Path.Combine(Application.dataPath, "Characters/Abyss")))
        {
            var abyssFiles = Directory.GetFiles(Path.Combine(Application.dataPath, "Characters/Abyss"), "*", SearchOption.AllDirectories);
            if (abyssFiles.Length > 0)
            {
                report.AppendLine("✅ Abyss: Character files found (" + abyssFiles.Length + " files)");
            }
            else
            {
                warnings.Add("Abyss folder exists but appears empty");
                report.AppendLine("⚠️  Abyss: Folder exists but appears empty");
            }
        }
        else
        {
            issues.Add("Abyss character folder missing: " + abyssPath);
            report.AppendLine("❌ Abyss: Character folder not found");
        }
        
        var sweetPath = "Assets/Characters/Sweet/Character_output.fbx";
        if (File.Exists(Path.Combine(Application.dataPath, "../", sweetPath)))
        {
            report.AppendLine("✅ Sweet: Character_output.fbx found");
        }
        else
        {
            warnings.Add("Sweet character file missing: " + sweetPath);
            report.AppendLine("⚠️  Sweet: Character_output.fbx not found");
        }
        
        var animPath = "Assets/Animations/Animation_Walking_withSkin.fbx";
        if (File.Exists(Path.Combine(Application.dataPath, "../", animPath)))
        {
            report.AppendLine("✅ Animations: Animation_Walking_withSkin.fbx found");
        }
        else
        {
            warnings.Add("Animation file missing: " + animPath);
            report.AppendLine("⚠️  Animations: Animation_Walking_withSkin.fbx not found");
        }
        
        report.AppendLine();
        report.AppendLine("🛠️  Editor Scripts:");
        
        // Check editor scripts
        var editorScripts = new string[]
        {
            "AutoConfigureHumanoid.cs",
            "CreateVRMScene.cs", 
            "QuickVRMMenu.cs",
            "ProjectValidator.cs"
        };
        
        foreach (var script in editorScripts)
        {
            if (File.Exists(Path.Combine(Application.dataPath, "Editor", script)))
            {
                report.AppendLine("✅ " + script + ": Installed");
            }
            else
            {
                issues.Add("Editor script missing: " + script);
                report.AppendLine("❌ " + script + ": Missing");
            }
        }
        
        report.AppendLine();
        report.AppendLine("🎬 Scenes:");
        
        // Check scenes
        var scenePath = "Assets/Scenes/VRM_Export_Main.unity";
        if (File.Exists(Path.Combine(Application.dataPath, "../", scenePath)))
        {
            report.AppendLine("✅ VRM_Export_Main.unity: Ready");
        }
        else
        {
            warnings.Add("Export scene missing - use 'VRM Tools → Create Empty VRM Scene'");
            report.AppendLine("⚠️  VRM_Export_Main.unity: Missing (use 'VRM Tools → Create Empty VRM Scene')");
        }
        
        // Check export folder
        var exportPath = Path.Combine(Application.dataPath, "Exports");
        if (Directory.Exists(exportPath))
        {
            report.AppendLine("✅ Exports folder: Ready");
        }
        else
        {
            Directory.CreateDirectory(exportPath);
            report.AppendLine("✅ Exports folder: Created");
        }
        
        report.AppendLine();
        report.AppendLine("📊 Summary:");
        report.AppendLine($"   Issues: {issues.Count}");
        report.AppendLine($"   Warnings: {warnings.Count}");
        
        if (issues.Count == 0 && warnings.Count == 0)
        {
            report.AppendLine();
            report.AppendLine("🎉 PROJECT READY FOR VRM EXPORT!");
            report.AppendLine();
            report.AppendLine("Next steps:");
            report.AppendLine("1. VRM Tools → Create Empty VRM Scene");
            report.AppendLine("2. Drag character into scene");
            report.AppendLine("3. VRM Tools → One-Click Export Selected Character");
        }
        else if (issues.Count == 0)
        {
            report.AppendLine();
            report.AppendLine("✅ PROJECT MOSTLY READY - Minor warnings only");
        }
        else
        {
            report.AppendLine();
            report.AppendLine("❌ ISSUES NEED ATTENTION");
            report.AppendLine();
            report.AppendLine("Critical Issues:");
            foreach (var issue in issues)
            {
                report.AppendLine("  • " + issue);
            }
        }
        
        if (warnings.Count > 0)
        {
            report.AppendLine();
            report.AppendLine("Warnings:");
            foreach (var warning in warnings)
            {
                report.AppendLine("  • " + warning);
            }
        }
        
        Debug.Log(report.ToString());
        
        // Show in dialog
        var dialogTitle = issues.Count == 0 ? "Validation Passed" : "Validation Issues";
        EditorUtility.DisplayDialog(dialogTitle, report.ToString(), "OK");
    }
    
    [MenuItem("VRM Tools/Quick Health Check")]
    public static void QuickHealthCheck()
    {
        var hasUniVRM = System.Array.Exists(System.AppDomain.CurrentDomain.GetAssemblies(), 
            a => a.FullName.Contains("VRM"));
        var hasGrimm = File.Exists(Path.Combine(Application.dataPath, "../Assets/Characters/Grimm/grimm.glb"));
        var hasAbyss = Directory.Exists(Path.Combine(Application.dataPath, "Characters/Abyss"));
        
        var status = "🔍 Quick Health Check:\n\n";
        status += (hasUniVRM ? "✅" : "❌") + " UniVRM Package\n";
        status += (hasGrimm ? "✅" : "❌") + " Grimm Character\n";
        status += (hasAbyss ? "✅" : "❌") + " Abyss Character\n";
        
        if (hasUniVRM && hasGrimm && hasAbyss)
        {
            status += "\n🎉 Ready to export VRMs!";
        }
        else
        {
            status += "\n⚠️  Run full validation for details";
        }
        
        EditorUtility.DisplayDialog("Health Check", status, "OK");
    }
}
