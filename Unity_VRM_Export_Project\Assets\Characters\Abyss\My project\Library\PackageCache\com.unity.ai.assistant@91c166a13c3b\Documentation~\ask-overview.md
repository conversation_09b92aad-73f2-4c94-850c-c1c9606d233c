---
uid: ask-overview
---

# Use /ask mode

The **/ask** mode retrieves information from Unity’s documentation, project settings, and preferences but doesn't modify anything in the project.

The key features of the **/ask** mode are as follows:

* Retrieves Unity API and Editor documentation.
* Provides answers about GameObjects, components, and project settings.
* Attaches GameObjects, components, or console errors for contextual responses.

## Query with /ask mode

To interact with Assistant through the **/ask** mode, follow these steps:

1. To begin a new conversation, select **+ Chat**.
1. Click inside the text field at the bottom of the Assistant window.
1. Enter your question or query in the text field.
1. Press **Enter** on your keyboard or select the send icon to submit your query. For example, `How do I add a 2D collider to a GameObject?`.

   You can also [Enable and use the Ctrl+Enter (macOS: ⌘Return) Preferences option](xref:preferences) to send your prompt to Assistant.

   While Assistant processes your query, the send icon changes to a stop icon so that you can cancel the request if needed.

   Assistant processes your input and displays a response. The response includes links to user guides, tutorials, and other helpful documents under [**Sources**](xref:assistant-sources).

1. Read the response and, if needed, type follow-up questions in the text field.
1. Continue the conversation until you get the desired information or help.
1. To switch to **/run** mode, select **Run Command**.

    Assistant generates a preview of the actions or tasks. For more information on how to use the **/run** mode, refer to [Use /run mode](xref:run-overview).

## Additional resources

* [Enable and use the Ctrl+Enter (macOS: ⌘Return) Preferences option](xref:preferences)
* [Assistant interface](xref:assistant-interface)
* [Best practices for using Assistant](xref:assistant-best)