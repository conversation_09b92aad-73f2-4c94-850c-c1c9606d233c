%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9167874883656233139
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5485954d14dfb9a4c8ead8edb0ded5b1, type: 3}
  m_Name: LiftGammaGain
  m_EditorClassIdentifier: 
  active: 1
  lift:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  gamma:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  gain:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
--- !u!114 &-8270506406425502121
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70afe9e12c7a7ed47911bb608a23a8ff, type: 3}
  m_Name: SplitToning
  m_EditorClassIdentifier: 
  active: 1
  shadows:
    m_OverrideState: 1
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  highlights:
    m_OverrideState: 1
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  balance:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-8104416584915340131
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: CopyPasteTestComponent2
  m_EditorClassIdentifier: Unity.RenderPipelines.Core.Editor.Tests:UnityEditor.Rendering.Tests:VolumeComponentCopyPasteTests/CopyPasteTestComponent2
  active: 1
  p1:
    m_OverrideState: 1
    m_Value: 0
  p2:
    m_OverrideState: 1
    m_Value: 0
  p21:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-7750755424749557576
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 60f3b30c03e6ba64d9a27dc9dba8f28d, type: 3}
  m_Name: OutlineVolumeComponent
  m_EditorClassIdentifier: 
  active: 1
  Enabled:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-7743500325797982168
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccf1aba9553839d41ae37dd52e9ebcce, type: 3}
  m_Name: MotionBlur
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0
  clamp:
    m_OverrideState: 1
    m_Value: 0.05
--- !u!114 &-7274224791359825572
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0fd9ee276a1023e439cf7a9c393195fa, type: 3}
  m_Name: TestAnimationCurveVolumeComponent
  m_EditorClassIdentifier: 
  active: 1
  testParameter:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0.5
        value: 10
        inSlope: 0
        outSlope: 10
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 15
        inSlope: 10
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
--- !u!114 &-6335409530604852063
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 66f335fb1ffd8684294ad653bf1c7564, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  postExposure:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  colorFilter:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  hueShift:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-6288072647309666549
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 29fa0085f50d5e54f8144f766051a691, type: 3}
  m_Name: FilmGrain
  m_EditorClassIdentifier: 
  active: 1
  type:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0
  response:
    m_OverrideState: 1
    m_Value: 0.8
  texture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-5520245016509672950
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97c23e3b12dc18c42a140437e53d3951, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  neutralHDRRangeReductionMode:
    m_OverrideState: 1
    m_Value: 2
  acesPreset:
    m_OverrideState: 1
    m_Value: 3
  hueShiftAmount:
    m_OverrideState: 1
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 1
    m_Value: 0
  paperWhite:
    m_OverrideState: 1
    m_Value: 300
  detectBrightnessLimits:
    m_OverrideState: 1
    m_Value: 1
  minNits:
    m_OverrideState: 1
    m_Value: 0.005
  maxNits:
    m_OverrideState: 1
    m_Value: 1000
--- !u!114 &-5360449096862653589
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: VolumeComponentSupportedEverywhere
  m_EditorClassIdentifier: Unity.RenderPipelines.Core.Editor.Tests:UnityEngine.Rendering.Tests:VolumeComponentEditorSupportedOnTests/VolumeComponentSupportedEverywhere
  active: 1
--- !u!114 &-5139089513906902183
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a00a63fdd6bd2a45ab1f2d869305ffd, type: 3}
  m_Name: OasisFogVolumeComponent
  m_EditorClassIdentifier: 
  active: 1
  Density:
    m_OverrideState: 1
    m_Value: 0
  StartDistance:
    m_OverrideState: 1
    m_Value: 0
  HeightRange:
    m_OverrideState: 1
    m_Value: {x: 0, y: 50}
  Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  SunScatteringIntensity:
    m_OverrideState: 1
    m_Value: 2
--- !u!114 &-4463884970436517307
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb60a22f311433c4c962b888d1393f88, type: 3}
  m_Name: PaniniProjection
  m_EditorClassIdentifier: 
  active: 1
  distance:
    m_OverrideState: 1
    m_Value: 0
  cropToFit:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-1410297666881709256
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6bd486065ce11414fa40e631affc4900, type: 3}
  m_Name: ProbeVolumesOptions
  m_EditorClassIdentifier: 
  active: 1
  normalBias:
    m_OverrideState: 1
    m_Value: 0.33
  viewBias:
    m_OverrideState: 1
    m_Value: 0
  scaleBiasWithMinProbeDistance:
    m_OverrideState: 1
    m_Value: 0
  samplingNoise:
    m_OverrideState: 1
    m_Value: 0.1
  animateSamplingNoise:
    m_OverrideState: 1
    m_Value: 1
  leakReductionMode:
    m_OverrideState: 1
    m_Value: 1
  minValidDotProductValue:
    m_OverrideState: 1
    m_Value: 0.1
  occlusionOnlyReflectionNormalization:
    m_OverrideState: 1
    m_Value: 1
  intensityMultiplier:
    m_OverrideState: 1
    m_Value: 1
  skyOcclusionIntensityMultiplier:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-1216621516061285780
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  skipIterations:
    m_OverrideState: 1
    m_Value: 1
  threshold:
    m_OverrideState: 1
    m_Value: 0.9
  intensity:
    m_OverrideState: 1
    m_Value: 0
  scatter:
    m_OverrideState: 1
    m_Value: 0.7
  clamp:
    m_OverrideState: 1
    m_Value: 65472
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  highQualityFiltering:
    m_OverrideState: 1
    m_Value: 0
  downscale:
    m_OverrideState: 1
    m_Value: 0
  maxIterations:
    m_OverrideState: 1
    m_Value: 6
  dirtTexture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  dirtIntensity:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-1170528603972255243
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 221518ef91623a7438a71fef23660601, type: 3}
  m_Name: WhiteBalance
  m_EditorClassIdentifier: 
  active: 1
  temperature:
    m_OverrideState: 1
    m_Value: 0
  tint:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-581120513425526550
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: CopyPasteTestComponent3
  m_EditorClassIdentifier: Unity.RenderPipelines.Core.Editor.Tests:UnityEditor.Rendering.Tests:VolumeComponentCopyPasteTests/CopyPasteTestComponent3
  active: 1
  p1:
    m_OverrideState: 1
    m_Value: 0
  p2:
    m_OverrideState: 1
    m_Value: 0
  p31:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: DefaultVolumeProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: -9167874883656233139}
  - {fileID: 1918650496244738858}
  - {fileID: 853819529557874667}
  - {fileID: 1052315754049611418}
  - {fileID: -1170528603972255243}
  - {fileID: -8270506406425502121}
  - {fileID: -5520245016509672950}
  - {fileID: 7173750748008157695}
  - {fileID: 1666464333004379222}
  - {fileID: 9001657382290151224}
  - {fileID: -6335409530604852063}
  - {fileID: -1216621516061285780}
  - {fileID: 3959858460715838825}
  - {fileID: -7743500325797982168}
  - {fileID: 4644742534064026673}
  - {fileID: -4463884970436517307}
  - {fileID: -6288072647309666549}
  - {fileID: 7518938298396184218}
  - {fileID: -1410297666881709256}
  - {fileID: -7750755424749557576}
  - {fileID: -5139089513906902183}
--- !u!114 &853819529557874667
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06437c1ff663d574d9447842ba0a72e4, type: 3}
  m_Name: ScreenSpaceLensFlare
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  tintColor:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  bloomMip:
    m_OverrideState: 1
    m_Value: 1
  firstFlareIntensity:
    m_OverrideState: 1
    m_Value: 1
  secondaryFlareIntensity:
    m_OverrideState: 1
    m_Value: 1
  warpedFlareIntensity:
    m_OverrideState: 1
    m_Value: 1
  warpedFlareScale:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1}
  samples:
    m_OverrideState: 1
    m_Value: 1
  sampleDimmer:
    m_OverrideState: 1
    m_Value: 0.5
  vignetteEffect:
    m_OverrideState: 1
    m_Value: 1
  startingPosition:
    m_OverrideState: 1
    m_Value: 1.25
  scale:
    m_OverrideState: 1
    m_Value: 1.5
  streaksIntensity:
    m_OverrideState: 1
    m_Value: 0
  streaksLength:
    m_OverrideState: 1
    m_Value: 0.5
  streaksOrientation:
    m_OverrideState: 1
    m_Value: 0
  streaksThreshold:
    m_OverrideState: 1
    m_Value: 0.25
  resolution:
    m_OverrideState: 1
    m_Value: 4
  chromaticAbberationIntensity:
    m_OverrideState: 1
    m_Value: 0.5
--- !u!114 &1052315754049611418
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 558a8e2b6826cf840aae193990ba9f2e, type: 3}
  m_Name: ShadowsMidtonesHighlights
  m_EditorClassIdentifier: 
  active: 1
  shadows:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  midtones:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  highlights:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  shadowsStart:
    m_OverrideState: 1
    m_Value: 0
  shadowsEnd:
    m_OverrideState: 1
    m_Value: 0.3
  highlightsStart:
    m_OverrideState: 1
    m_Value: 0.55
  highlightsEnd:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &1666464333004379222
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3eb4b772797da9440885e8bd939e9560, type: 3}
  m_Name: ColorCurves
  m_EditorClassIdentifier: 
  active: 1
  master:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  red:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  green:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  blue:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsHue:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 1
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 1
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  satVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  lumVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
--- !u!114 &1918650496244738858
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e021b4c809a781e468c2988c016ebbea, type: 3}
  m_Name: ColorLookup
  m_EditorClassIdentifier: 
  active: 1
  texture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  contribution:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &3959858460715838825
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c01700fd266d6914ababb731e09af2eb, type: 3}
  m_Name: DepthOfField
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  gaussianStart:
    m_OverrideState: 1
    m_Value: 10
  gaussianEnd:
    m_OverrideState: 1
    m_Value: 30
  gaussianMaxRadius:
    m_OverrideState: 1
    m_Value: 1
  highQualitySampling:
    m_OverrideState: 1
    m_Value: 0
  focusDistance:
    m_OverrideState: 1
    m_Value: 10
  aperture:
    m_OverrideState: 1
    m_Value: 5.6
  focalLength:
    m_OverrideState: 1
    m_Value: 50
  bladeCount:
    m_OverrideState: 1
    m_Value: 5
  bladeCurvature:
    m_OverrideState: 1
    m_Value: 1
  bladeRotation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &4251301726029935498
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 74955a4b0b4243bc87231e8b59ed9140, type: 3}
  m_Name: TestVolume
  m_EditorClassIdentifier: 
  active: 1
  param:
    m_OverrideState: 1
    m_Value: 123
--- !u!114 &4644742534064026673
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81180773991d8724ab7f2d216912b564, type: 3}
  m_Name: ChromaticAberration
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &6940869943325143175
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: VolumeComponentSupportedOnAnySRP
  m_EditorClassIdentifier: Unity.RenderPipelines.Core.Editor.Tests:UnityEngine.Rendering.Tests:VolumeComponentEditorSupportedOnTests/VolumeComponentSupportedOnAnySRP
  active: 1
--- !u!114 &7173750748008157695
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  color:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0
  smoothness:
    m_OverrideState: 1
    m_Value: 0.2
  rounded:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &7518938298396184218
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5e1dc532bcb41949b58bc4f2abfbb7e, type: 3}
  m_Name: LensDistortion
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  xMultiplier:
    m_OverrideState: 1
    m_Value: 1
  yMultiplier:
    m_OverrideState: 1
    m_Value: 1
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  scale:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &9001657382290151224
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdfbdbb87d3286943a057f7791b43141, type: 3}
  m_Name: ChannelMixer
  m_EditorClassIdentifier: 
  active: 1
  redOutRedIn:
    m_OverrideState: 1
    m_Value: 100
  redOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
  redOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
  greenOutRedIn:
    m_OverrideState: 1
    m_Value: 0
  greenOutGreenIn:
    m_OverrideState: 1
    m_Value: 100
  greenOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutRedIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutBlueIn:
    m_OverrideState: 1
    m_Value: 100
--- !u!114 &9122958982931076880
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: CopyPasteTestComponent1
  m_EditorClassIdentifier: Unity.RenderPipelines.Core.Editor.Tests:UnityEditor.Rendering.Tests:VolumeComponentCopyPasteTests/CopyPasteTestComponent1
  active: 1
  p1:
    m_OverrideState: 1
    m_Value: 0
  p2:
    m_OverrideState: 1
    m_Value: 0
