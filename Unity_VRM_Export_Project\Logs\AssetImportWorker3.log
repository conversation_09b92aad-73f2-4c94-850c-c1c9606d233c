Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 16193 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1-x86_64\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
C:/Users/<USER>/Desktop/Vtube - Copy/Unity_VRM_Export_Project
-logFile
Logs/AssetImportWorker3.log
-srvPort
64544
Successfully changed project path to: C:/Users/<USER>/Desktop/Vtube - Copy/Unity_VRM_Export_Project
C:/Users/<USER>/Desktop/Vtube - Copy/Unity_VRM_Export_Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20704] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4130551857 [EditorId] 4130551857 [Version] 1048832 [Id] WindowsEditor(7,ATLAS) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [20704] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4130551857 [EditorId] 4130551857 [Version] 1048832 [Id] WindowsEditor(7,ATLAS) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 47.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1-x86_64/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Desktop/Vtube - Copy/Unity_VRM_Export_Project/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2882)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1-x86_64/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1-x86_64/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1-x86_64/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56536
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1-x86_64/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006203 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 66.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.829 seconds
Domain Reload Profiling:
	ReloadAssembly (830ms)
		BeginReloadAssembly (130ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (591ms)
			LoadAssemblies (128ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (127ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (38ms)
			SetupLoadedEditorAssemblies (373ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (66ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (213ms)
				ProcessInitializeOnLoadMethodAttributes (81ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.006270 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 65.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.059 seconds
Domain Reload Profiling:
	ReloadAssembly (2060ms)
		BeginReloadAssembly (131ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (18ms)
		EndReloadAssembly (1779ms)
			LoadAssemblies (112ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (294ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1224ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (6ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (66ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (1039ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Shader 'VRM10/Universal Render Pipeline/MToon10': fallback shader 'Hidden/Universal Render Pipeline/FallbackError' not found
WARNING: Shader Unsupported: 'VRM10/Universal Render Pipeline/MToon10' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'VRM10/Universal Render Pipeline/MToon10' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader 'VRM10/Universal Render Pipeline/MToon10': fallback shader 'Hidden/Universal Render Pipeline/FallbackError' not found
WARNING: Shader Unsupported: 'VRM10/Universal Render Pipeline/MToon10' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'VRM10/Universal Render Pipeline/MToon10' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3856 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (114.9 KB). Loaded Objects now: 4316.
Memory consumption went from 137.4 MB to 137.3 MB.
Total: 5.990800 ms (FindLiveObjects: 0.316400 ms CreateObjectMapping: 0.223600 ms MarkObjects: 5.257400 ms  DeleteObjects: 0.191600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26671.789869 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/TextureSamplerUtil.cs
  artifactKey: Guid(62f23c5ca623a9f4083c25a63b2c82af) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/TextureSamplerUtil.cs using Guid(62f23c5ca623a9f4083c25a63b2c82af) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'd9e85f47e3973cc831512d1442357575') in 0.102962 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Import/TextureDescriptor.cs
  artifactKey: Guid(405597f56d6540347bbecb1203aba033) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Import/TextureDescriptor.cs using Guid(405597f56d6540347bbecb1203aba033) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'c4b22e6be7e7f26b76da66a41615a85b') in 0.006420 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/Import/MaterialDescriptorGeneratorUtility.cs
  artifactKey: Guid(2a7306f577654254b1fda4dedc322f87) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/Import/MaterialDescriptorGeneratorUtility.cs using Guid(2a7306f577654254b1fda4dedc322f87) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'aaf3567c572fe361d4f488ca269c7ae6') in 0.006329 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/VRM10/Runtime/IO/Material/URP/Export/Materials/UrpVrm10MToonMaterialExporter.cs
  artifactKey: Guid(7d97f9c3e4a2454e829349d47f8bd1f7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/Runtime/IO/Material/URP/Export/Materials/UrpVrm10MToonMaterialExporter.cs using Guid(7d97f9c3e4a2454e829349d47f8bd1f7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '23fb6d9e5af082dbd35ae819e4364189') in 0.005845 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/BuiltInRP/Import/Materials/BuiltInGltfDefaultMaterialImporter.cs
  artifactKey: Guid(aa5431a3a9ee4f9caac398688c3a8973) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/BuiltInRP/Import/Materials/BuiltInGltfDefaultMaterialImporter.cs using Guid(aa5431a3a9ee4f9caac398688c3a8973) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '20084a905fde04d2055d5291f4adb00e') in 0.006183 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Import/TextureFactory.cs
  artifactKey: Guid(4d6069f2bfbfc0c4c9276e8a5f8b0789) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Import/TextureFactory.cs using Guid(4d6069f2bfbfc0c4c9276e8a5f8b0789) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '97b7ea6a2e08d54aa226af896066b848') in 0.006242 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Import/DeserializingTextureInfo.cs
  artifactKey: Guid(83f927a8b2da47b793985326c2e7bd96) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Import/DeserializingTextureInfo.cs using Guid(83f927a8b2da47b793985326c2e7bd96) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'f117434b273115ac28e5c9994a33ef21') in 0.005821 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/Utils/InternalMaterialUtils.cs
  artifactKey: Guid(cc1b44f784074898b268882d75b86e23) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/Utils/InternalMaterialUtils.cs using Guid(cc1b44f784074898b268882d75b86e23) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'ff7012fcd29a5299309c6eecd5118fda') in 0.006619 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Import/KtxTextureDeserializer.cs
  artifactKey: Guid(76c69cc9d1f84e988ca6fb6391bee3cb) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Import/KtxTextureDeserializer.cs using Guid(76c69cc9d1f84e988ca6fb6391bee3cb) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'a1afb7592a82f7baba330be23f42f897') in 0.006571 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/VRM10/Runtime/IO/Material/URP/Export/UrpVrm10MaterialExporter.cs
  artifactKey: Guid(95b7c97505db9d747b04136aa1505204) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/Runtime/IO/Material/URP/Export/UrpVrm10MaterialExporter.cs using Guid(95b7c97505db9d747b04136aa1505204) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'd8b2f41b0edc6bc646463879c2dde72c') in 0.005597 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/RenderPipelineTypes.cs
  artifactKey: Guid(87bf5e0c19fe0584084fcddfd5294ad3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/RenderPipelineTypes.cs using Guid(87bf5e0c19fe0584084fcddfd5294ad3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '3446d68f98d4f7905dc1a957757a8a97') in 0.005001 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/VRM10/Runtime/Components/Vrm10Runtime/Springbone/Vrm10FastSpringboneRuntimeStandalone.cs
  artifactKey: Guid(b2db989bb92a9a64b98516b30db3902a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/Runtime/Components/Vrm10Runtime/Springbone/Vrm10FastSpringboneRuntimeStandalone.cs using Guid(b2db989bb92a9a64b98516b30db3902a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'a360e46131e9d64c968526b30f36d105') in 0.006570 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/BuiltInRP/Export/Materials/BuiltInFallbackMaterialExporter.cs
  artifactKey: Guid(983277a982364497b7491610ac4868f0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/BuiltInRP/Export/Materials/BuiltInFallbackMaterialExporter.cs using Guid(983277a982364497b7491610ac4868f0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '9ca5b35d5257b11dd63ccb21ac6aae8e') in 0.005211 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Characters/Abyss/My project/Assets/TextMesh Pro/Shaders/TMP_Sprite.shader
  artifactKey: Guid(cf81c85f95fe47e1a27f6ae460cf182c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/Characters/Abyss/My project/Assets/TextMesh Pro/Shaders/TMP_Sprite.shader using Guid(cf81c85f95fe47e1a27f6ae460cf182c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '1c4ffd1917d982209789f4aad41a8e23') in 0.027737 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/UniGLTF/Runtime/Scenes/LookDev/cubemap/ballroom_1k.exr
  artifactKey: Guid(cb5229eb8a46ed14daf2be5f1292b6c0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/Scenes/LookDev/cubemap/ballroom_1k.exr using Guid(cb5229eb8a46ed14daf2be5f1292b6c0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '0b12395895d74cfd700bea82667ab3ef') in 0.062694 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/VRM10/MToon10/Runtime/UnityShaderLab/MToonDefinedValues/MToon10AlphaMode.cs
  artifactKey: Guid(4915ab0041b841b6aa0cdfecae764008) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/MToon10/Runtime/UnityShaderLab/MToonDefinedValues/MToon10AlphaMode.cs using Guid(4915ab0041b841b6aa0cdfecae764008) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'be7bc70dff5c2570861ad3517f2034e8') in 0.007997 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MeshIO/PrimitiveExtensions.cs
  artifactKey: Guid(867e7d22991044cd921d5479269142f2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MeshIO/PrimitiveExtensions.cs using Guid(867e7d22991044cd921d5479269142f2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '2601252f5e1763edaf31627776c79897') in 0.006145 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/VRM10/MToon10/Runtime/UnityShaderLab/MToonDefinedValues/MToon10TransparentWithZWriteMode.cs
  artifactKey: Guid(d5df515a36d3460891b348872459476f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/MToon10/Runtime/UnityShaderLab/MToonDefinedValues/MToon10TransparentWithZWriteMode.cs using Guid(d5df515a36d3460891b348872459476f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'a8055db6e2a11d88ca25c16c3082fa79') in 0.007354 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Characters/Abyss/My project/Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset
  artifactKey: Guid(2e498d1c8094910479dc3e1b768306a4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/Characters/Abyss/My project/Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset using Guid(2e498d1c8094910479dc3e1b768306a4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '54433eb763c416f78883f8265ee2490c') in 0.069428 seconds 
Number of asset objects unloaded after import = 5
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Export/TextureExporter.cs
  artifactKey: Guid(65fdfff6cc4b1e14a882259e903fc830) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Export/TextureExporter.cs using Guid(65fdfff6cc4b1e14a882259e903fc830) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '27368853b854d3086b206dcd70e75b3c') in 0.006982 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/VRM10/MToon10/Runtime/UnityShaderLab/MToonDefinedValues/MToon10NormalMapKeyword.cs
  artifactKey: Guid(78ea3fe03cac402083bd53882d186b95) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/MToon10/Runtime/UnityShaderLab/MToonDefinedValues/MToon10NormalMapKeyword.cs using Guid(78ea3fe03cac402083bd53882d186b95) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'dd7ccd2ac2092306a5925aa6f7ec3f1d') in 0.006481 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Characters/Abyss/My project/Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF.asset
  artifactKey: Guid(8f586378b4e144a9851e7b34d9b748ee) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/Characters/Abyss/My project/Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF.asset using Guid(8f586378b4e144a9851e7b34d9b748ee) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '319330731f69800a15f8a5a051698979') in 0.040887 seconds 
Number of asset objects unloaded after import = 7
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/UniGLTF/Runtime/UniHumanoid/PoseModifier/HandPose.cs
  artifactKey: Guid(14d18bc5a296a894eb7154d3f5f0e18b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniHumanoid/PoseModifier/HandPose.cs using Guid(14d18bc5a296a894eb7154d3f5f0e18b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '269611b78409bfdd705f2b0a86339dfe') in 0.005683 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/TextureTransform.cs
  artifactKey: Guid(a652f0fdb4236434a9a64aa35a3cb44f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/TextureTransform.cs using Guid(a652f0fdb4236434a9a64aa35a3cb44f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '6f4809466e14cb1ce037008132777ad8') in 0.005818 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/UniGLTF/Runtime/UniJSON/Extensions/ByteExtensions.cs
  artifactKey: Guid(aa32ad3ea4c1ec540a03225c0d520af9) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniJSON/Extensions/ByteExtensions.cs using Guid(aa32ad3ea4c1ec540a03225c0d520af9) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '1851ecc3b684281831db09c977d8d0d0') in 0.004780 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/VRM10/Editor/Components/Expression/ReorderableMorphTargetBindingList.cs
  artifactKey: Guid(b82126a68b53be745936ee0115e3708a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/Editor/Components/Expression/ReorderableMorphTargetBindingList.cs using Guid(b82126a68b53be745936ee0115e3708a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '7d2959cc43580757951ba548d21717a6') in 0.005116 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/UniGLTF/Runtime/UniJSON/ListTreeNode/ListTreeNode.cs
  artifactKey: Guid(26f90c47d985b3e4c8cf153607f9fb31) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniJSON/ListTreeNode/ListTreeNode.cs using Guid(26f90c47d985b3e4c8cf153607f9fb31) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'b96441978281f5342a9de0a1aa2b6c72') in 0.004932 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/Symbols.cs
  artifactKey: Guid(ffb4175afef01ea48ad7669a8b12d93f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/Symbols.cs using Guid(ffb4175afef01ea48ad7669a8b12d93f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'c71406c078993399741111888248a3b8') in 0.005225 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/VRM10/Runtime/Components/VRM10Object/VRM10Object.cs
  artifactKey: Guid(88684271e27adb843b6570957c9e7637) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/Runtime/Components/VRM10Object/VRM10Object.cs using Guid(88684271e27adb843b6570957c9e7637) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '92ff18ac44dea71088edae226b5251e9') in 0.005028 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/VRM10/Runtime/Format/Animation/Format.g.cs
  artifactKey: Guid(f595db5658d457449b2fa63cfdc29c6d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/Runtime/Format/Animation/Format.g.cs using Guid(f595db5658d457449b2fa63cfdc29c6d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '55379b44770216c7009936010d0beddc') in 0.005793 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/VRM10/Runtime/IO/Model/MeshReader.cs
  artifactKey: Guid(2f4647a633dfc844eb1b8e7563addba2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/Runtime/IO/Model/MeshReader.cs using Guid(2f4647a633dfc844eb1b8e7563addba2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'ded6c5371cb82642350f262a2d632ca7') in 0.005751 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MeshIO/MeshVertexUtility.cs
  artifactKey: Guid(6cf1507df80846d0aeb9ab663677881a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MeshIO/MeshVertexUtility.cs using Guid(6cf1507df80846d0aeb9ab663677881a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '6b23059834fefee0d61bb26e5c9fe3c6') in 0.004285 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/VRM10/Runtime/IO/Material/URP/Import/Materials/UrpVrm10MToonMaterialImporter.cs
  artifactKey: Guid(57d820ccc1ff7e2469f7fb5cc435bd5b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/Runtime/IO/Material/URP/Import/Materials/UrpVrm10MToonMaterialImporter.cs using Guid(57d820ccc1ff7e2469f7fb5cc435bd5b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'e2e9b1a8897dc61a72643bbea210b14f') in 0.005514 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Export/TextureExportTypes.cs
  artifactKey: Guid(bf56f43baf1e4b47becb7d0ba42a72dc) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/TextureIO/Export/TextureExportTypes.cs using Guid(bf56f43baf1e4b47becb7d0ba42a72dc) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '4cc380b53ab27821e802fcce51223677') in 0.005843 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/URP/Export/Materials/UrpLitMaterialExporter.cs
  artifactKey: Guid(8832f807c30a420d95aeaee15a1fbff7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/URP/Export/Materials/UrpLitMaterialExporter.cs using Guid(8832f807c30a420d95aeaee15a1fbff7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'dd14913a1c24b66740f5d48cd95c5a8f') in 0.006462 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/URP/ShaderUtil/Lit/UrpUnlitContext.cs
  artifactKey: Guid(3fe15309261045ba937d6a8dafb6a93d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/URP/ShaderUtil/Lit/UrpUnlitContext.cs using Guid(3fe15309261045ba937d6a8dafb6a93d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'd8c5f002a1b34f1e9d78a6e1873acadc') in 0.005484 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/URP/Import/Materials/UrpGltfDefaultMaterialImporter.cs
  artifactKey: Guid(320b6d26ea014356b1c37e38425bf152) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/URP/Import/Materials/UrpGltfDefaultMaterialImporter.cs using Guid(320b6d26ea014356b1c37e38425bf152) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'ceeb9ccf8fc229509a939f1c308ef604') in 0.007989 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/URP/ShaderUtil/Lit/UrpLitContext.cs
  artifactKey: Guid(c15ac3fb36ff4e7d8ec271e2cf1fb263) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/UniGLTF/Runtime/UniGLTF/IO/MaterialIO/URP/ShaderUtil/Lit/UrpLitContext.cs using Guid(c15ac3fb36ff4e7d8ec271e2cf1fb263) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: '76df3d30add8e44eb7067915bfe5d662') in 0.007190 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/VRM10/MToon10/Runtime/UnityShaderLab/MToonDefinedValues/MToon10OutlineMode.cs
  artifactKey: Guid(8aece52be2cb42de9bfde4a072e5a6a0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Number of updated assets reloaded before import = 0
Start importing Assets/VRM10/MToon10/Runtime/UnityShaderLab/MToonDefinedValues/MToon10OutlineMode.cs using Guid(8aece52be2cb42de9bfde4a072e5a6a0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)  -> (artifact id: 'de2020e1e333de9d219d6b5f78d400a4') in 0.006229 seconds 
Number of asset objects unloaded after import = 0
