using System;
using System.Collections.Generic;
using UnityEngine;

namespace UniVRM10
{
    public interface IVrm10Animation : IDisposable
    {
        (INormalized<PERSON><PERSON><PERSON><PERSON><PERSON>, ITPoseProvider) ControlRig { get; }
        IReadOnlyDictionary<ExpressionKey, Func<float>> ExpressionMap { get; }
        public void ShowBoxMan(bool enable);
        public void SetBoxManMaterial(Material material);
        LookAtInput? LookAt { get; }
    }
}
