* [About Assistant](xref:assistant-intro)
   * [Assistant interface](xref:assistant-interface)
       * [Enable and use the Ctrl+Enter (macOS: ⌘Return) Preferences option](xref:preferences)
   * [Install Assistant with the Package Manager](xref:install-assistant)
* [Get started with Assistant](xref:get-started)
   * [Use /ask mode](xref:ask-overview)
   * [Use /run mode](xref:run-overview)
   * [Use /code mode](xref:code-overview)
 * [Manage Assistant](xref:manage-assistant)
   * [Use Assistant history](xref:assistant-history)
   * [Manage messages and code snippets in Assistant](xref:assistant-code)
   * [Use sources in Assistant](xref:assistant-sources)
   * [Provide feedback in Assistant](xref:assistant-feedback)
   * [Interact with GameObjects, assets, and console errors](xref:assistant-object-query)
* [Best practices for using Assistant](xref:assistant-best)