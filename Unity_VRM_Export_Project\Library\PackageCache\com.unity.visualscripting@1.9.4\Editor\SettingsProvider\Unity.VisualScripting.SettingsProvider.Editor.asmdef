{"name": "Unity.VisualScripting.SettingsProvider.Editor", "references": ["Unity.VisualScripting.Core", "Unity.VisualScripting.Core.Editor", "Unity.VisualScripting.Flow", "Unity.VisualScripting.Flow.Editor", "Unity.VisualScripting.State"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}