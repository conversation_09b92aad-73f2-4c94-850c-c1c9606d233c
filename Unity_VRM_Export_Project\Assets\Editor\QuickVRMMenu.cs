#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using System.Linq;
using System.IO;

/// <summary>
/// Quick access menu for VRM export workflow.
/// Provides helper functions to streamline the character-to-VRM pipeline.
/// </summary>
public static class QuickVRMMenu
{
    [MenuItem("VRM Tools/Select Imported Character Root")]
    public static void SelectCharacterRoot()
    {
        // Find character root objects in the current scene
        var roots = UnityEngine.SceneManagement.SceneManager.GetActiveScene().GetRootGameObjects();
        GameObject bestCandidate = null;
        
        foreach (var go in roots)
        {
            // Look for objects with SkinnedMeshRenderer (character meshes)
            var skinned = go.GetComponentInChildren<SkinnedMeshRenderer>();
            if (skinned != null)
            {
                // Prefer objects that look like character names
                if (go.name.ToLower().Contains("grimm") || 
                    go.name.ToLower().Contains("abyss") || 
                    go.name.ToLower().Contains("character"))
                {
                    bestCandidate = go;
                    break;
                }
                else if (bestCandidate == null)
                {
                    bestCandidate = go;
                }
            }
        }
        
        if (bestCandidate != null)
        {
            Selection.activeGameObject = bestCandidate;
            
            // Focus scene view on the character
            if (SceneView.lastActiveSceneView != null)
            {
                SceneView.lastActiveSceneView.FrameSelected();
            }
            
            Debug.Log($"[QuickVRMMenu] Selected character root: {bestCandidate.name}");
            
            // Check if it's ready for VRM export
            ValidateCharacterForExport(bestCandidate);
        }
        else
        {
            Debug.LogWarning("[QuickVRMMenu] No character root with SkinnedMeshRenderer found. Drag a model prefab into the scene first.");
            EditorUtility.DisplayDialog(
                "No Character Found",
                "No character with a SkinnedMeshRenderer found in the scene.\n\n" +
                "Steps to fix:\n" +
                "1. Drag your character prefab from Assets/Characters/ into the scene\n" +
                "2. Make sure it's positioned at the origin (0,0,0)\n" +
                "3. Try this command again",
                "OK"
            );
        }
    }
    
    [MenuItem("VRM Tools/Validate Selected Character")]
    public static void ValidateSelectedCharacter()
    {
        var selected = Selection.activeGameObject;
        if (selected == null)
        {
            EditorUtility.DisplayDialog("No Selection", "Please select a character first.", "OK");
            return;
        }
        
        ValidateCharacterForExport(selected);
    }
    
    private static void ValidateCharacterForExport(GameObject character)
    {
        var issues = new System.Collections.Generic.List<string>();
        var warnings = new System.Collections.Generic.List<string>();
        
        // Check for Animator component
        var animator = character.GetComponent<Animator>();
        if (animator == null)
        {
            issues.Add("Missing Animator component");
        }
        else if (animator.avatar == null)
        {
            issues.Add("Animator has no Avatar assigned");
        }
        else if (!animator.avatar.isHuman)
        {
            issues.Add("Avatar is not configured as Humanoid");
        }
        
        // Check for SkinnedMeshRenderer
        var skinnedMeshes = character.GetComponentsInChildren<SkinnedMeshRenderer>();
        if (skinnedMeshes.Length == 0)
        {
            issues.Add("No SkinnedMeshRenderer found");
        }
        
        // Check materials
        foreach (var smr in skinnedMeshes)
        {
            foreach (var material in smr.materials)
            {
                if (material == null)
                {
                    warnings.Add("Some materials are missing");
                    break;
                }
            }
        }
        
        // Check scale
        if (character.transform.localScale != Vector3.one)
        {
            warnings.Add($"Character scale is not (1,1,1): {character.transform.localScale}");
        }
        
        // Report results
        if (issues.Count == 0)
        {
            var message = $"✅ {character.name} is ready for VRM export!";
            if (warnings.Count > 0)
            {
                message += "\n\nWarnings:\n" + string.Join("\n", warnings);
            }
            
            Debug.Log($"[QuickVRMMenu] {message}");
            EditorUtility.DisplayDialog("Validation Passed", message, "OK");
        }
        else
        {
            var message = $"❌ {character.name} has issues that need fixing:\n\n" + string.Join("\n", issues);
            if (warnings.Count > 0)
            {
                message += "\n\nWarnings:\n" + string.Join("\n", warnings);
            }
            
            Debug.LogError($"[QuickVRMMenu] {message}");
            EditorUtility.DisplayDialog("Validation Failed", message, "OK");
        }
    }
    
    [MenuItem("VRM Tools/Quick Material Setup (MToon)")]
    public static void SetupMToonMaterials()
    {
        var selected = Selection.activeGameObject;
        if (selected == null)
        {
            EditorUtility.DisplayDialog("No Selection", "Please select a character first.", "OK");
            return;
        }
        
        var renderers = selected.GetComponentsInChildren<Renderer>();
        int materialCount = 0;
        
        foreach (var renderer in renderers)
        {
            foreach (var material in renderer.materials)
            {
                if (material != null && !material.shader.name.Contains("VRM"))
                {
                    // Try to find MToon shader
                    var mtoonShader = Shader.Find("VRM/MToon");
                    if (mtoonShader != null)
                    {
                        material.shader = mtoonShader;
                        materialCount++;
                    }
                }
            }
        }
        
        if (materialCount > 0)
        {
            Debug.Log($"[QuickVRMMenu] Applied MToon shader to {materialCount} materials");
            EditorUtility.DisplayDialog("Materials Updated", $"Applied MToon shader to {materialCount} materials.", "OK");
        }
        else
        {
            EditorUtility.DisplayDialog("No Changes", "MToon shader not found or already applied. Make sure UniVRM is imported.", "OK");
        }
    }
    
    [MenuItem("VRM Tools/How to Export (Read Me)")]
    public static void HowToExport()
    {
        EditorUtility.DisplayDialog(
            "VRM Export Steps",
            "📋 Complete VRM Export Workflow:\n\n" +
            "1️⃣ Import UniVRM (.unitypackage) via Assets → Import Package\n" +
            "2️⃣ Drag your GLB/FBX (e.g., grimm.glb, hooded figure with sythe.glb) into Assets/Characters/\n" +
            "3️⃣ Open Assets/Scenes/VRM_Export_Main.unity (or create via VRM Tools → Create Empty VRM Scene)\n" +
            "4️⃣ Drag your character prefab into the scene\n" +
            "5️⃣ Select the character (VRM Tools → Select Imported Character Root)\n" +
            "6️⃣ Validate character (VRM Tools → Validate Selected Character)\n" +
            "7️⃣ Top menu: VRM0 → Export UniVRM-0.x → fill metadata → Export to Assets/Exports/\n\n" +
            "🔧 If rig mapping fails:\n" +
            "• Set Model → Rig → Animation Type: Humanoid and Apply\n" +
            "• If still red, fix bones in Blender and re-export\n\n" +
            "🎨 For better visuals:\n" +
            "• Use VRM Tools → Quick Material Setup (MToon)",
            "OK");
    }
    
    [MenuItem("VRM Tools/Open Exports Folder")]
    public static void OpenExportsFolder()
    {
        var path = Application.dataPath + "/Exports";
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }

        EditorUtility.RevealInFinder(path);
        Debug.Log($"[QuickVRMMenu] Opened exports folder: {path}");
    }

    [MenuItem("VRM Tools/One-Click Export Selected Character")]
    public static void OneClickExportVRM()
    {
        var selected = Selection.activeGameObject;
        if (selected == null)
        {
            EditorUtility.DisplayDialog("No Selection", "Please select a character first.", "OK");
            return;
        }

        // Validate character first
        if (!IsCharacterValidForExport(selected))
        {
            EditorUtility.DisplayDialog(
                "Character Not Ready",
                "The selected character has issues. Use 'Validate Selected Character' to see details.",
                "OK"
            );
            return;
        }

        // Auto-generate export path
        var characterName = selected.name.Replace("(Clone)", "").Trim();
        var exportPath = Path.Combine(Application.dataPath, "Exports", $"{characterName}.vrm");

        // Show export dialog with pre-filled data
        var proceed = EditorUtility.DisplayDialog(
            "Export VRM",
            $"Ready to export: {characterName}\n" +
            $"Export path: {exportPath}\n\n" +
            "This will open the UniVRM export window with the character selected.\n" +
            "You'll need to fill in the metadata (Title, Author, License) and click Export.",
            "Continue",
            "Cancel"
        );

        if (proceed)
        {
            // Try to open UniVRM export window
            // Note: This requires UniVRM to be imported
            try
            {
                // Look for UniVRM export menu item
                var exportMethod = System.Type.GetType("VRM.VRMExportSettings, VRM")?.GetMethod("CreateAsset");
                if (exportMethod != null)
                {
                    Debug.Log($"[QuickVRMMenu] Opening UniVRM export for {characterName}");
                    EditorUtility.DisplayDialog(
                        "Export Ready",
                        "Character validated! Now:\n\n" +
                        "1. Go to top menu: VRM0 → Export UniVRM-0.x\n" +
                        "2. Fill in metadata:\n" +
                        "   - Title: " + characterName + "\n" +
                        "   - Author: Your Name\n" +
                        "   - License: Choose appropriate\n" +
                        "3. Set export path to Assets/Exports/\n" +
                        "4. Click Export",
                        "Got It"
                    );
                }
                else
                {
                    EditorUtility.DisplayDialog(
                        "UniVRM Not Found",
                        "UniVRM package not detected. Please:\n\n" +
                        "1. Import UniVRM package first\n" +
                        "2. Then use: VRM0 → Export UniVRM-0.x from top menu",
                        "OK"
                    );
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[QuickVRMMenu] Error accessing UniVRM: {e.Message}");
                EditorUtility.DisplayDialog(
                    "Export Helper",
                    "Character is ready for export!\n\n" +
                    "Manual steps:\n" +
                    "1. Top menu: VRM0 → Export UniVRM-0.x\n" +
                    "2. Fill metadata and export to Assets/Exports/",
                    "OK"
                );
            }
        }
    }

    private static bool IsCharacterValidForExport(GameObject character)
    {
        // Quick validation check
        var animator = character.GetComponent<Animator>();
        if (animator == null || animator.avatar == null || !animator.avatar.isHuman)
            return false;

        var skinnedMeshes = character.GetComponentsInChildren<SkinnedMeshRenderer>();
        if (skinnedMeshes.Length == 0)
            return false;

        return true;
    }
}
#endif
