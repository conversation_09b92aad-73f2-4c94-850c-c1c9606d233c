﻿// This file is generated from JsonSchema. Don't modify this source code.
using System;
using System.Collections.Generic;


namespace UniGLTF.Extensions.VRMC_vrm_animation
{

    public class HumanBone
    {
        // Dictionary object with extension-specific objects.
        public object Extensions;

        // Application-specific data.
        public object Extras;

        // Represents a single glTF node mapped to this humanBone.
        public int? Node;
    }

    public class HumanBones
    {
        // Dictionary object with extension-specific objects.
        public object Extensions;

        // Application-specific data.
        public object Extras;

        // Represents a single bone of a Humanoid.
        public HumanBone Hips;

        // Represents a single bone of a Humanoid.
        public HumanBone Spine;

        // Represents a single bone of a Humanoid.
        public HumanBone Chest;

        // Represents a single bone of a Humanoid.
        public HumanBone UpperChest;

        // Represents a single bone of a Humanoid.
        public HumanBone Neck;

        // Represents a single bone of a Humanoid.
        public HumanBone Head;

        // Represents a single bone of a Humanoid.
        public HumanBone Jaw;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftUpperLeg;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftLowerLeg;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftFoot;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftToes;

        // Represents a single bone of a Humanoid.
        public HumanBone RightUpperLeg;

        // Represents a single bone of a Humanoid.
        public HumanBone RightLowerLeg;

        // Represents a single bone of a Humanoid.
        public HumanBone RightFoot;

        // Represents a single bone of a Humanoid.
        public HumanBone RightToes;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftShoulder;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftUpperArm;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftLowerArm;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftHand;

        // Represents a single bone of a Humanoid.
        public HumanBone RightShoulder;

        // Represents a single bone of a Humanoid.
        public HumanBone RightUpperArm;

        // Represents a single bone of a Humanoid.
        public HumanBone RightLowerArm;

        // Represents a single bone of a Humanoid.
        public HumanBone RightHand;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftThumbMetacarpal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftThumbProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftThumbDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftIndexProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftIndexIntermediate;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftIndexDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftMiddleProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftMiddleIntermediate;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftMiddleDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftRingProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftRingIntermediate;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftRingDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftLittleProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftLittleIntermediate;

        // Represents a single bone of a Humanoid.
        public HumanBone LeftLittleDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightThumbMetacarpal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightThumbProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightThumbDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightIndexProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightIndexIntermediate;

        // Represents a single bone of a Humanoid.
        public HumanBone RightIndexDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightMiddleProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightMiddleIntermediate;

        // Represents a single bone of a Humanoid.
        public HumanBone RightMiddleDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightRingProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightRingIntermediate;

        // Represents a single bone of a Humanoid.
        public HumanBone RightRingDistal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightLittleProximal;

        // Represents a single bone of a Humanoid.
        public HumanBone RightLittleIntermediate;

        // Represents a single bone of a Humanoid.
        public HumanBone RightLittleDistal;
    }

    public class Humanoid
    {
        // Dictionary object with extension-specific objects.
        public object Extensions;

        // Application-specific data.
        public object Extras;

        // An object which maps humanoid bones to nodes.
        public HumanBones HumanBones;
    }

    public class Expression
    {
        // Dictionary object with extension-specific objects.
        public object Extensions;

        // Application-specific data.
        public object Extras;

        // Represents a single glTF node mapped to this expression.
        public int? Node;
    }

    public class Preset
    {
        // Represents a single expression.
        public Expression Happy;

        // Represents a single expression.
        public Expression Angry;

        // Represents a single expression.
        public Expression Sad;

        // Represents a single expression.
        public Expression Relaxed;

        // Represents a single expression.
        public Expression Surprised;

        // Represents a single expression.
        public Expression Aa;

        // Represents a single expression.
        public Expression Ih;

        // Represents a single expression.
        public Expression Ou;

        // Represents a single expression.
        public Expression Ee;

        // Represents a single expression.
        public Expression Oh;

        // Represents a single expression.
        public Expression Blink;

        // Represents a single expression.
        public Expression BlinkLeft;

        // Represents a single expression.
        public Expression BlinkRight;

        // Represents a single expression.
        public Expression Neutral;
    }

    public class Expressions
    {
        // Dictionary object with extension-specific objects.
        public object Extensions;

        // Application-specific data.
        public object Extras;

        // An object that contains definitions of preset expressions.
        public Preset Preset;

        // An object that contains definitions of custom expressions.
        public Dictionary<string, Expression> Custom;
    }

    public class LookAt
    {
        // Dictionary object with extension-specific objects.
        public object Extensions;

        // Application-specific data.
        public object Extras;

        // Represents a single glTF node represents the eye gaze point.
        public int? Node;

        // The position offset of the origin of the LookAt from the humanoid head bone
        public float[] OffsetFromHeadBone;
    }

    public class VRMC_vrm_animation
    {
        public const string ExtensionName = "VRMC_vrm_animation";

        // Dictionary object with extension-specific objects.
        public object Extensions;

        // Application-specific data.
        public object Extras;

        // Specification version of VRMC_vrm_animation
        public string SpecVersion;

        // An object which describes about humanoid bones.
        public Humanoid Humanoid;

        // An object which maps expressions to nodes.
        public Expressions Expressions;

        // An object which maps a eye gaze point to a node.
        public LookAt LookAt;
    }
}
