2025-09-24T08:38:13.309Z INFO SharedStateManager initialized
2025-09-24T08:38:13.310Z INFO [Migration] Checking for database migration...
2025-09-24T08:38:13.476Z INFO [Migration] No migration needed
2025-09-24T08:38:13.477Z INFO [MainProvider] Creating new MainProvider instance...
2025-09-24T08:38:13.482Z INFO [c:\Users\<USER>\.vscode\extensions\pythagoratechnologies.pythagora-vs-code-2.5.4] Project root:
2025-09-24T08:38:13.482Z INFO [c:\Users\<USER>\.vscode\extensions\pythagoratechnologies.pythagora-vs-code-2.5.4\prisma\schema.prisma] Schema path:
2025-09-24T08:38:13.482Z INFO [file:/pythagora/pythagora-core/data/database/pythagora.db] Database URL:
2025-09-24T08:38:13.483Z INFO [true] Schema exists:
2025-09-24T08:38:13.484Z INFO Running prisma generate first...
2025-09-24T08:38:16.489Z INFO [Prisma schema loaded from prisma\schema.prisma

✔ Generated Prisma Client (6.16.2) to .\src\generated\prisma in 309ms

] Prisma generate result:
2025-09-24T08:38:16.490Z INFO Running prisma db push to sync schema...
2025-09-24T08:38:18.609Z INFO [Prisma schema loaded from prisma\schema.prisma
Datasource "db": SQLite database "pythagora.db" at "file:/pythagora/pythagora-core/data/database/pythagora.db"

The database is already in sync with the Prisma schema.

Running generate... (Use --skip-generate to skip the generators)
[2K[1A[2K[GRunning generate... - Prisma Client
[2K[1A[2K[G✔ Generated Prisma Client (6.16.2) to .\src\generated\prisma in 412ms

] Prisma db push result:
2025-09-24T08:38:18.610Z INFO Is remote: false
2025-09-24T08:38:18.611Z INFO Starting Local. Installation path: undefined
2025-09-24T08:44:11.117Z INFO [[object Object]] MainProvider handleMessage
2025-09-24T08:44:12.885Z INFO [[object Object]] MainProvider handleMessage
2025-09-24T08:44:12.886Z INFO [[object Object]] Center panel message:
2025-09-24T08:44:12.887Z INFO [[object Object]] MainProvider handleMessage
2025-09-24T08:44:12.887Z INFO [[object Object]] Center panel message:
2025-09-24T08:44:12.887Z INFO [MainProvider] Center panel ready - processing ready handler
2025-09-24T08:44:12.888Z INFO [MainProvider] Center panel ready - flushing 0 buffered messages
2025-09-24T08:44:12.888Z INFO [Message Router] Routing getProjectInfo through unified router
2025-09-24T08:44:14.248Z INFO MainProvider: Center panel disposed
