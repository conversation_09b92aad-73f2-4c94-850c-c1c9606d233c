---
uid: assistant-history
---

# Use Assistant history

Assistant saves your full conversation history so that you can access your interactions at any time, across sessions. To review previous interactions, select the **History** button. Browse through your past queries and responses to find the relevant information.

The **History** tab includes a search bar at the top to locate specific conversations or messages within your chat history.

## Mark a conversation as Favorites

Mark important conversations as **Favorites** for frequent access and reference.

To mark a conversation as **Favorite**, follow these steps:

1. In the **History** window, find the conversation you want to mark as a favorite.

2. Select the star icon next to the conversation.

   This marks the conversation as a favorite and pins it to the top of the **History** window.

3. To remove a conversation as a favorite, select the star icon next to the conversation that's currently marked as a favorite.

    The star icon is no longer highlighted, indicating that the conversation isn't marked as a favorite. The conversation is removed from the pinned section at the top of the **History** window.

## Delete a conversation

You can delete a conversation from the **History** tab. This action is permanent and can't be undone.

To delete a conversation from the **History** tab, follow these steps:

1. Select the conversation you want to delete from the list.
2. Right-click the conversation and select **Delete**.

## Rename a conversation

To identify conversation topics and purposes without opening the chat history, give conversations meaningful names.

To rename a conversation from the **History** tab, follow these steps:

1. Select the conversation you want to rename from the list.
2. Right-click the conversation and select **Edit**.
3. Enter a new name for the conversation.

## Additional resources

* [Use sources in Assistant](xref:assistant-sources)
* [Provide feedback in Assistant](xref:assistant-feedback)