---
uid: assistant-intro
---

# About Assistant

Assistant is a generative artificial intelligence (AI) tool integrated into the Unity Editor to help you automate tasks, generate code, and access project-specific information.

Assistant supports three modes of operation for handling queries and tasks:

* [**/ask**](xref:ask-overview) mode: provides answers, references documentation, or retrieves project-specific details without changing your project files.
* [**/run**](xref:run-overview) mode: automates repetitive tasks by generating functional scripts that can execute changes directly in the Unity Editor.
* [**/code**](xref:code-overview) mode: generates and reviews customizable C# code snippets that interact with the Unity API.

For best results, use Assistant with Unity 6 or later.

## Prerequisites

Before you can use the **AI** menu and tools in the Unity Editor, ensure that:

* Your project is using Unity 6.2 (6000.2).
* You agreed to and accepted the terms and conditions of the **AI** menu in the Unity Editor.

For more information, refer to [AI menu in Unity Editor](https://docs.unity3d.com/6000.2/Documentation/Manual/ai-menu.html).

Use the [Unity Dashboard AI settings](https://cloud.unity.com/home/<USER>/settings) to monitor your AI usage, manage your point balance, and configure settings across your organization. This helps you understand how often Generators are used, control costs, and ensure the right team members have access to the right tools. For more information, refer to the [Unity Dashboard AI settings](https://docs.unity.com/en-us/ai) documentation.

Use the following topics to get started with using Assistant.

| Topic | Description |
| ----- | ----------- |
| [Assistant interface](xref:assistant-interface) | Understand the main Assistant window and how to use it. |
| [Get started with Assistant](xref:get-started) | Learn about Assistant's three different modes of operation. |
| [Install Assistant with the Package Manager](xref:install-assistant) | Use the Package Manager to install Assistant. |
| [Best practices for using Assistant](xref:assistant-best) | Use the best practice tips to become an advanced user. |

## Additional resources

* [Use /ask mode](xref:ask-overview)
* [Use /run mode](xref:run-overview)
* [Use /code mode](xref:code-overview)