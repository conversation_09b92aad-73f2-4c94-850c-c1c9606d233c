using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

/// <summary>
/// Creates a clean scene optimized for VRM character export and testing.
/// Includes proper lighting and ground plane for character preview.
/// </summary>
public static class CreateVRMScene
{
    [MenuItem("VRM Tools/Create Empty VRM Scene")]
    public static void CreateScene()
    {
        // Create new scene with default objects (camera, light)
        var scene = EditorSceneManager.NewScene(NewSceneSetup.DefaultGameObjects, NewSceneMode.Single);
        scene.name = "VRM_Export_Main";

        // Configure main camera for character viewing
        var camera = Camera.main;
        if (camera != null)
        {
            camera.transform.position = new Vector3(0, 1.5f, 3);
            camera.transform.rotation = Quaternion.Euler(10, 0, 0);
            camera.backgroundColor = new Color(0.2f, 0.2f, 0.3f); // Nice dark blue background
            
            Debug.Log("[CreateVRMScene] Configured main camera for character viewing");
        }

        // Ensure we have proper lighting
        var existingLight = GameObject.FindObjectOfType<Light>();
        if (existingLight == null)
        {
            var lightGO = new GameObject("Directional Light");
            var light = lightGO.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1.0f;
            light.shadows = LightShadows.Soft;
            lightGO.transform.rotation = Quaternion.Euler(50, -30, 0);
            
            Debug.Log("[CreateVRMScene] Added directional light");
        }
        else
        {
            // Configure existing light for character viewing
            existingLight.intensity = 1.0f;
            existingLight.shadows = LightShadows.Soft;
            existingLight.transform.rotation = Quaternion.Euler(50, -30, 0);
            
            Debug.Log("[CreateVRMScene] Configured existing light");
        }

        // Add fill light for better character illumination
        var fillLightGO = new GameObject("Fill Light");
        var fillLight = fillLightGO.AddComponent<Light>();
        fillLight.type = LightType.Directional;
        fillLight.intensity = 0.3f;
        fillLight.shadows = LightShadows.None;
        fillLight.color = new Color(0.8f, 0.9f, 1.0f); // Slightly blue tint
        fillLightGO.transform.rotation = Quaternion.Euler(-20, 150, 0);

        // Create ground plane for reference
        var ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.localScale = Vector3.one * 5f;
        ground.transform.position = Vector3.zero;
        
        // Make ground material more neutral
        var groundRenderer = ground.GetComponent<Renderer>();
        if (groundRenderer != null)
        {
            var groundMaterial = new Material(Shader.Find("Standard"));
            groundMaterial.color = new Color(0.5f, 0.5f, 0.5f);
            groundMaterial.SetFloat("_Metallic", 0f);
            groundMaterial.SetFloat("_Glossiness", 0.1f);
            groundRenderer.material = groundMaterial;
        }

        // Add character spawn point marker
        var spawnPoint = new GameObject("Character Spawn Point");
        spawnPoint.transform.position = new Vector3(0, 0, 0);
        
        // Add a small visual indicator
        var indicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        indicator.name = "Spawn Indicator";
        indicator.transform.SetParent(spawnPoint.transform);
        indicator.transform.localPosition = Vector3.zero;
        indicator.transform.localScale = new Vector3(0.1f, 0.01f, 0.1f);
        
        var indicatorRenderer = indicator.GetComponent<Renderer>();
        if (indicatorRenderer != null)
        {
            var indicatorMaterial = new Material(Shader.Find("Standard"));
            indicatorMaterial.color = Color.green;
            indicatorMaterial.SetFloat("_Mode", 3); // Transparent mode
            indicatorMaterial.color = new Color(0, 1, 0, 0.5f);
            indicatorRenderer.material = indicatorMaterial;
        }

        // Save the scene
        var scenePath = "Assets/Scenes/VRM_Export_Main.unity";
        EditorSceneManager.SaveScene(scene, scenePath);
        
        Debug.Log($"[CreateVRMScene] Created and saved scene: {scenePath}");
        Debug.Log("[CreateVRMScene] Scene ready for character import and VRM export");
        
        // Show helpful message
        EditorUtility.DisplayDialog(
            "VRM Scene Created", 
            $"Created scene: {scenePath}\n\n" +
            "Next steps:\n" +
            "1. Drag your character prefab into the scene\n" +
            "2. Position it at the green spawn point\n" +
            "3. Use 'VRM Tools → Select Imported Character Root'\n" +
            "4. Export via 'VRM0 → Export UniVRM-0.x'", 
            "OK"
        );
    }
    
    [MenuItem("VRM Tools/Setup Scene Lighting")]
    public static void SetupSceneLighting()
    {
        // Quick lighting setup for existing scenes
        var lights = GameObject.FindObjectsOfType<Light>();
        
        if (lights.Length == 0)
        {
            CreateScene();
            return;
        }
        
        // Configure existing lights for character viewing
        foreach (var light in lights)
        {
            if (light.type == LightType.Directional)
            {
                light.intensity = 1.0f;
                light.shadows = LightShadows.Soft;
                break;
            }
        }
        
        Debug.Log("[CreateVRMScene] Configured scene lighting for character viewing");
    }
}
