# Node reference

> [!NOTE]
> For versions 2019/2020 LTS, download the Visual Scripting package from the [Unity Asset Store](https://assetstore.unity.com/packages/tools/visual-bolt-163802).

Nodes are the most basic element of computation in visual scripting. Nodes display the required information as text, but editing is done via the Inspector. To edit them, select any node and edit its properties in the Inspector.

## This node

The [This](vs-self.md) node returns the game object that owns the machine in which the graph runs.

## Control nodes

[Control](vs-control.md) nodes branch, loop and merge the flow.

## Time  nodes

[Time](vs-time.md) nodes include timer, cooldown and wait nodes.

## Events

Scripting nodes listen for [events](vs-events-reference.md). They are the starting point for all scripts and appear as special green nodes in graphs.

## Variables

These nodes get, set, and check [variables](vs-variables-reference.md).

## Nulls

Nodes that deal with the [nulls](vs-nulls.md), a.k.a. "nothing" value.

## Formulas

[Formula](vs-formula.md) evaluates logical and mathematical expressions directly via a textual Formula and match with multiple arguments.
