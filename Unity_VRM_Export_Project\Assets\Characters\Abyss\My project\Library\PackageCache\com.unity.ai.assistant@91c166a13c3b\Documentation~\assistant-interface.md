---
uid: assistant-interface
---

# Assistant interface

This section provides a detailed tour of Assistant.

![Assistant editor window with marked labels](Images/assistant-interface.png)

| Component | Description |
| --------- | ----------- |
| **A**: **+ Chat** |  Start a new conversation with Assistant or reset the current chat context. |
| **B**: **History** |  Refer to a log of your previous interactions within Assistant. Review and reuse previous information without having to retype or recall past conversations. |
| **C**: Text field | Enter your queries. Queries are the primary interaction point for communication with Assistant. By typing in questions, instructions, or topics of interest, you start the conversation and receive relevant responses from Assistant. |
| **D**: **Shortcuts** | Opens a window with slash commands to switch between different modes, such as **/ask**, **/run**, or **/code**. |
| **E**: **Attach** | Attach different types of assets, GameObjects, Console logs and errors, MonoBehaviour scripts, and other project-specific files. |
| **F**: Refresh icon | Clears the visible list of prior responses or refreshes the Assistant's interface. |

## Additional resources

* [Use /ask mode](xref:ask-overview)
* [Best practices for using Assistant](xref:assistant-best)