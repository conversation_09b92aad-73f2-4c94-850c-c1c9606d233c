# Develop application logic with Script Graphs

Use Script Graphs to create interactions and logic in your project.

## Create a graph file 

Create a graph file to get started. For more information, see [Create a new graph file](vs-create-graph.md).

## Add and connect nodes 

After you have a graph file, [add a node](vs-add-node-to-graph.md) or [connect nodes together](vs-creating-connections.md) to build logic. 

## Create Subgraphs 

Reuse logic with [Subgraphs](vs-nesting-add-subgraph.md).

## Debug your graphs 

You can [use relations](vs-relations.md) to help you debug your scripts, or use Visual Scripting's [predictive debugging](vs-debugging.md) to help you catch problems. 
