{"name": "com.unity.ai.assistant", "displayName": "Assistant", "version": "1.0.0-pre.12", "unity": "6000.2", "description": "Use AI for project-aware information retrieval, troubleshooting, code generation, assistance, and scene editing.\n\nAsk\nSolve hard problems or get ideas for your project. Assistant provides you with relevant answers based on the context of your project.\n\nRun\nAutomate actions in the Editor to tackle repetitive tasks and streamline workflows. Use natural language to scatter objects across terrain, adjust lighting, rename assets, restructure project files, and more.\n\nCode\nReduce the time and effort required to structure gameplay mechanics by using <PERSON> to create pre-compiled scripts.", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.serialization": "3.1.1", "com.unity.ai.toolkit": "1.0.0-pre.18"}, "unityRelease": "0b4", "_upm": {"changelog": "- Update AI-Toolkit to 1.0.0-pre.18"}, "upmCi": {"footprint": "bbe8727d31205c0e27cf37fc15350a02268f1513"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.ai.assistant@1.0/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/muse-editor.git", "type": "git", "revision": "81800939ecf2017f2b6b1afd61a107665d743008"}, "_fingerprint": "91c166a13c3bef5814c5a13a69fa442a657dd539"}