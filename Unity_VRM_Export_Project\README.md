# Unity VRM Export Project — Scaffolding Pack

This is a **drop-in scaffold** for converting your 3D character models into VRM avatars for VTubing and streaming applications.

## 🎯 What This Project Does

Converts your character files into VRM format:
- **<PERSON>** (`grimm.glb`) → `Grimm.vrm`
- **Mr. Abyss** (`hooded figure with sythe.glb`) → `Abyss.vrm`
- **Sweet/others** (`Character_output.fbx`) → `Character.vrm`
- **Animations** (`Animation_Walking_withSkin.fbx`) → Reusable motion data

## 📋 Prerequisites

- **Unity 2021.3 LTS** (recommended by UniVRM)
- **UniVRM package** (download from official UniVRM releases)

## 🚀 Quick Setup

1. **Create Unity Project**: New 3D project named `VRM_Export_Project`
2. **Copy Structure**: Copy all folders from this scaffold into your Unity `Assets/` folder
3. **Import UniVRM**: `Assets → Import Package → Custom Package...` → select UniVRM `.unitypackage`
4. **Import Your Models**: 
   - Copy `grimm.glb` → `Assets/Characters/Grimm/`
   - Copy `hooded figure with sythe.glb` → `Assets/Characters/Abyss/`
   - Copy `Character_output.fbx` → `Assets/Characters/Sweet/`
   - Copy `Animation_Walking_withSkin.fbx` → `Assets/Animations/`

## 📁 Project Structure

```
Assets/
  Scenes/
    VRM_Export_Main.unity          # Main export scene
  Characters/
    Grimm/                         # Grimm assets
    Abyss/                         # Mr. Abyss assets  
    Sweet/                         # Other characters
  Animations/                      # Animation clips
  Exports/                         # Final .vrm files
  Editor/                          # Automation scripts
    AutoConfigureHumanoid.cs       # Auto-rig imported models
    CreateVRMScene.cs              # Scene setup helper
    QuickVRMMenu.cs                # Export workflow menus
  Materials/                       # Shared materials
```

## 🔧 Workflow

1. **Create Scene**: `VRM Tools → Create Empty VRM Scene`
2. **Import Character**: Drag model from `Characters/` into scene
3. **Select Character**: `VRM Tools → Select Imported Character Root`
4. **Export VRM**: Top menu `VRM0 → Export UniVRM-0.x`
5. **Test**: Load `.vrm` in VSeeFace or Animaze

## 🎨 Material Setup

After import, assign **VRM/MToon** shaders for best results:
- Anime/toon shading
- Outline support
- MatCap highlights
- Optimized for real-time

## 🔍 Troubleshooting

**Rig Issues**: If bones are red/unmapped:
- Check Model → Rig → Animation Type: Humanoid
- If still failing, fix skeleton in Blender and re-export

**Scale Issues**: Adjust Scale Factor in import settings

**Missing Materials**: GLB preserves textures better than FBX

## 🎯 Integration with Abyss & Grimm Sidekick

These VRM avatars will integrate with your streaming overlay:
- Real-time lip-sync with ElevenLabs TTS
- Expression/emote triggers via WebSocket
- OBS/Streamlabs compatible
- Desktop overlay with draggable avatars

## 📝 Next Steps

1. Export your character VRMs
2. Test in VSeeFace
3. Integrate with your vtuber-autocaster system
4. Set up OBS sources for streaming

---

**Need Help?** Use `VRM Tools → How to Export (Read Me)` for step-by-step guidance.
