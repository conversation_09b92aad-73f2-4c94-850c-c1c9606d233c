---
uid: preferences
---

# Enable and use the Ctrl+Enter (macOS: ⌘Return) Preferences option

Use the **Ctrl**+**Enter** (macOS: **⌘Return**) option in Assistant to control how you submit prompts. It's useful when you work across different languages or workflows in which accidental prompt submissions can be  disruptive.

By default, when you press **Enter** (macOS: **Return**), the prompt is immediately sent to Assistant. This can be inconvenient if you're writing a long prompt, multi-line text, or code block that requires formatting. If you enable the **Ctrl**+**Enter** (macOS: **⌘Return**) option, it ensures that prompts are only sent to Assistant when you use the full key combination.

## Enable **Ctrl**+**Enter** (macOS: ⌘Return) option

The **Ctrl**+**Enter** (macOS: **⌘Return**) option modifies the behavior of the text field in Assistant. The text field no longer responds to **Enter** (macOS: **Return**) on its own.

To configure and enable the **Ctrl**+**Enter** (macOS: **⌘Return**) option in Assistant, follow these steps:

1. Open the Assistant window.
1. Do one of the following, depending on your platform:

   * **Windows**: Select **Edit** > **Preferences** > **AI** > **Assistant**.
   * **macOS**: Select **Unity** > **Settings** > **AI** > **Assistant**.

1. In the **Preferences** window, enable the following setting:

   * **Windows**: **Use Ctrl+Enter to send a prompt**.
   * **macOS**: **Use ⌘Return to send a prompt**.

After you enable this option, press **Ctrl**+**Enter** (macOS: **⌘Return**) together to send your prompt.

## Additional resources

* [Install Assistant with the Package Manager](xref:install-assistant)
* [Get started with Assistant](xref:get-started)