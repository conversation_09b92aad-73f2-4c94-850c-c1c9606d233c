# Unity VRM Export Project - Complete Setup Guide

## 🎯 Overview

This project converts your <PERSON><PERSON> & Grimm character models into VRM avatars for streaming and VTubing applications.

**Your Assets:**
- `grimm.glb` (Grimm character)
- `hooded figure with sythe.glb` (<PERSON>. <PERSON><PERSON>)  
- `Character_output.fbx` (Sweet character)
- `Animation_Walking_withSkin.fbx` (Walk animation)

**Target Output:**
- `Grimm.vrm` (Ready for VSeeFace/Animaze)
- `Abyss.vrm` (Ready for VSeeFace/Animaze)
- `Sweet.vrm` (Ready for VSeeFace/Animaze)

## 📋 Prerequisites

1. **Unity 2021.3 LTS** - Download from Unity Hub
2. **UniVRM Package** - Download from: https://github.com/vrm-c/UniVRM/releases
   - Get the latest `.unitypackage` file (e.g., `UniVRM-0.x.x_xxxx.unitypackage`)

## 🚀 Step-by-Step Setup

### Step 1: Create Unity Project
1. Open Unity Hub
2. Click "New Project"
3. Select "3D" template
4. Name: `VRM_Export_Project`
5. Location: Choose your preferred folder
6. Click "Create"

### Step 2: Import This Scaffold
1. Copy the entire `Assets/` folder from this scaffold into your Unity project's `Assets/` folder
2. Unity will automatically import and compile the scripts

### Step 3: Import UniVRM
1. In Unity: `Assets → Import Package → Custom Package...`
2. Select the downloaded `UniVRM-x.x.x_xxxx.unitypackage`
3. Click "Import" (import everything)
4. Wait for compilation to finish

### Step 4: Copy Your Character Assets

**Option A: Manual Copy**
```
Copy from your current project:
./PGD/grimm.glb → Unity_VRM_Export_Project/Assets/Characters/Grimm/
./mrabyss/hooded-figure-with-scythe/ → Unity_VRM_Export_Project/Assets/Characters/Abyss/
./biped/Character_output.fbx → Unity_VRM_Export_Project/Assets/Characters/Sweet/
./biped/Animation_Walking_withSkin.fbx → Unity_VRM_Export_Project/Assets/Animations/
```

**Option B: Use Copy Script** (see `copy_assets.bat` below)

### Step 5: First Export Test

1. **Create Scene**: `VRM Tools → Create Empty VRM Scene`
2. **Import Grimm**: 
   - In Project window, navigate to `Assets/Characters/Grimm/`
   - Drag `grimm.glb` into the scene
   - Position at origin (0,0,0)
3. **Select Character**: `VRM Tools → Select Imported Character Root`
4. **Validate**: `VRM Tools → Validate Selected Character`
5. **Export VRM**: 
   - Top menu: `VRM0 → Export UniVRM-0.x`
   - Fill in metadata:
     - Title: "Grimm"
     - Author: "Your Name"
     - License: Choose appropriate license
   - Export to `Assets/Exports/Grimm.vrm`

### Step 6: Test Your VRM
1. Download VSeeFace: https://www.vseeface.icu/
2. Open VSeeFace
3. Load your `Grimm.vrm` file
4. Test facial tracking and movement

## 🔧 Troubleshooting

### Rig Issues
**Problem**: Red bones in Avatar Configuration
**Solution**: 
1. Select your model in Project window
2. Inspector → Rig tab
3. Animation Type: Humanoid
4. Avatar Definition: Create From This Model
5. Click "Apply"
6. If still red, the skeleton needs fixing in Blender

### Scale Issues  
**Problem**: Character too large/small
**Solution**:
1. Select model in Project window
2. Inspector → Model tab
3. Scale Factor: Adjust (try 0.01 or 100)
4. Click "Apply"

### Material Issues
**Problem**: Character looks flat/wrong colors
**Solution**:
1. Select character in scene
2. `VRM Tools → Quick Material Setup (MToon)`
3. Manually assign textures if needed

### Missing Textures
**Problem**: White/pink materials
**Solution**:
1. Ensure texture files are in the same folder as your model
2. For GLB files, textures should be embedded
3. For FBX files, copy texture files to the same folder

## 🎨 Advanced Tips

### Better Lighting
- Use `VRM Tools → Setup Scene Lighting` for optimal character preview
- The scaffold includes proper 3-point lighting setup

### Animation Setup
1. Create Animator Controller in `Assets/Animations/`
2. Drag walk animation into controller
3. Assign controller to character's Animator component
4. Export VRM with animation setup

### Material Optimization
- Use VRM/MToon shader for best results
- Adjust Shade Color and Shade Shift for toon look
- Enable Outline for anime-style borders

## 🔗 Integration with Your Streaming Setup

Once you have VRM files:

1. **VSeeFace Integration**:
   - Load VRM in VSeeFace
   - Configure webcam tracking
   - Set up virtual camera output

2. **OBS Integration**:
   - Add VSeeFace virtual camera as source
   - Position avatar overlay
   - Configure with your vtuber-autocaster system

3. **Animaze Integration**:
   - Import VRM into Animaze
   - Configure WebSocket control
   - Connect to your TTS/expression system

## 📁 Final File Structure

```
Unity_VRM_Export_Project/
├── Assets/
│   ├── Characters/
│   │   ├── Grimm/grimm.glb
│   │   ├── Abyss/hooded figure with sythe.glb
│   │   └── Sweet/Character_output.fbx
│   ├── Animations/Animation_Walking_withSkin.fbx
│   ├── Exports/
│   │   ├── Grimm.vrm
│   │   ├── Abyss.vrm
│   │   └── Sweet.vrm
│   └── [UniVRM files]
```

## 🎯 Next Steps

1. Export all three characters as VRM
2. Test each VRM in VSeeFace
3. Integrate with your vtuber-autocaster overlay
4. Set up OBS sources for streaming
5. Configure TTS lip-sync and expressions

---

**Need Help?** Use the `VRM Tools` menu in Unity for guided assistance!
